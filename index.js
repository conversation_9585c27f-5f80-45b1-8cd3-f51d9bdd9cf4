/**
 * Information Bar Integration Tool - SillyTavern第三方扩展插件
 * 
 * 功能特性:
 * - 信息栏设置界面
 * - 数据表格管理
 * - 自定义API配置
 * - 智能数据管理
 * - 界面定制功能
 * 
 * @version 1.0.0
 * <AUTHOR> Bar Integration Tool Developer
 */

// 导入核心模块
import { UnifiedDataCore } from './core/UnifiedDataCore.js';
import { EventSystem } from './core/EventSystem.js';
import { ConfigManager } from './core/ConfigManager.js';
import { APIIntegration } from './core/APIIntegration.js';

// 导入UI组件
import { InfoBarSettings } from './ui/InfoBarSettings.js';
import { DataTable } from './ui/DataTable.js';

// 功能模块将在后续版本中添加
// import { ContentManager } from './modules/ContentManager.js';
// import { PanelManager } from './modules/PanelManager.js';
// import { DataManager } from './modules/DataManager.js';

// 扩展主类
class InformationBarIntegrationTool {
    constructor() {
        console.log('[InfoBarTool] 🚀 Information Bar Integration Tool 初始化开始');
        
        // 扩展标识
        this.MODULE_NAME = 'information_bar_integration_tool';
        this.VERSION = '1.0.0';
        
        // 核心模块
        this.dataCore = null;
        this.eventSystem = null;
        this.configManager = null;
        this.apiIntegration = null;
        
        // UI组件
        this.infoBarSettings = null;
        this.dataTable = null;
        
        // 功能模块 (将在后续版本中添加)
        // this.contentManager = null;
        // this.panelManager = null;
        // this.dataManager = null;
        
        // SillyTavern上下文
        this.context = null;
        
        // 初始化状态
        this.initialized = false;
        this.errorCount = 0;
        this.version = '1.0.0';
        
        // 绑定方法
        this.init = this.init.bind(this);
        this.onAppReady = this.onAppReady.bind(this);
        this.createUI = this.createUI.bind(this);
        this.handleError = this.handleError.bind(this);
    }

    /**
     * 初始化扩展
     */
    async init() {
        try {
            console.log('[InfoBarTool] 📊 开始初始化核心模块...');
            
            // 获取SillyTavern上下文
            this.context = SillyTavern.getContext();
            
            if (!this.context) {
                throw new Error('无法获取SillyTavern上下文');
            }
            
            // 初始化核心模块
            await this.initCoreModules();
            
            // 初始化UI组件
            await this.initUIComponents();
            
            // 初始化功能模块
            await this.initFunctionModules();
            
            // 监听SillyTavern事件
            this.bindSillyTavernEvents();
            
            // 创建用户界面
            this.createUI();
            
            this.initialized = true;
            console.log('[InfoBarTool] ✅ Information Bar Integration Tool 初始化完成');
            
            // 触发初始化完成事件
            this.eventSystem.emit('tool:initialized', {
                version: this.VERSION,
                timestamp: Date.now()
            });
            
        } catch (error) {
            console.error('[InfoBarTool] ❌ 初始化失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 初始化核心模块
     */
    async initCoreModules() {
        console.log('[InfoBarTool] 🔧 初始化核心模块...');
        
        // 初始化事件系统
        this.eventSystem = new EventSystem();
        
        // 初始化数据核心
        this.dataCore = new UnifiedDataCore(this.eventSystem);
        await this.dataCore.init();
        
        // 初始化配置管理器
        this.configManager = new ConfigManager(this.dataCore);
        await this.configManager.init();
        
        // 初始化API集成
        this.apiIntegration = new APIIntegration(this.configManager);
        await this.apiIntegration.init();
        
        console.log('[InfoBarTool] ✅ 核心模块初始化完成');
    }

    /**
     * 初始化UI组件
     */
    async initUIComponents() {
        console.log('[InfoBarTool] 🎨 初始化UI组件...');
        
        // 初始化信息栏设置界面
        this.infoBarSettings = new InfoBarSettings(
            this.configManager,
            this.apiIntegration,
            this.eventSystem
        );

        // 初始化数据表格界面
        this.dataTable = new DataTable(
            this.dataCore,
            this.configManager,
            this.eventSystem
        );

        // 创建modules对象以便外部访问
        this.modules = {
            settings: this.infoBarSettings,
            dataTable: this.dataTable,
            dataCore: this.dataCore,
            eventSystem: this.eventSystem,
            configManager: this.configManager,
            apiIntegration: this.apiIntegration
        };

        console.log('[InfoBarTool] ✅ UI组件初始化完成');
    }

    /**
     * 初始化功能模块 (将在后续版本中实现)
     */
    async initFunctionModules() {
        console.log('[InfoBarTool] ⚙️ 功能模块将在后续版本中添加...');

        // 功能模块将在后续版本中实现
        // this.contentManager = new ContentManager(this.dataCore, this.eventSystem);
        // this.panelManager = new PanelManager(this.dataCore, this.eventSystem);
        // this.dataManager = new DataManager(this.dataCore, this.eventSystem);

        console.log('[InfoBarTool] ✅ 功能模块初始化跳过');
    }

    /**
     * 绑定SillyTavern事件
     */
    bindSillyTavernEvents() {
        const { eventSource, event_types } = this.context;
        
        // 监听应用就绪事件
        eventSource.on(event_types.APP_READY, this.onAppReady);
        
        // 监听聊天切换事件
        eventSource.on(event_types.CHAT_CHANGED, (data) => {
            this.eventSystem.emit('chat:changed', data);
        });
        
        // 监听消息接收事件
        eventSource.on(event_types.MESSAGE_RECEIVED, (data) => {
            this.eventSystem.emit('message:received', data);
        });
        
        // 监听消息发送事件
        eventSource.on(event_types.MESSAGE_SENT, (data) => {
            this.eventSystem.emit('message:sent', data);
        });
        
        console.log('[InfoBarTool] 🔗 SillyTavern事件绑定完成');
    }

    /**
     * 应用就绪事件处理
     */
    onAppReady() {
        console.log('[InfoBarTool] 🎯 SillyTavern应用就绪');
        this.eventSystem.emit('app:ready');
    }

    /**
     * 创建用户界面
     */
    createUI() {
        console.log('[InfoBarTool] 🖼️ 创建用户界面...');
        
        try {
            // 获取正确的扩展菜单按钮容器
            const extensionContainer = document.querySelector('#extensionsMenuButton');

            if (!extensionContainer) {
                throw new Error('找不到扩展菜单按钮容器 #extensionsMenuButton');
            }

            console.log('[InfoBarTool] 📍 使用扩展容器:', extensionContainer.id || extensionContainer.className);
            
            // 查找或创建扩展菜单下拉列表
            let extensionMenu = extensionContainer.nextElementSibling;
            if (!extensionMenu || !extensionMenu.classList.contains('dropdown-menu')) {
                // 如果没有找到下拉菜单，查找父级容器中的菜单
                const parentContainer = extensionContainer.parentElement;
                extensionMenu = parentContainer?.querySelector('.dropdown-menu') ||
                              parentContainer?.querySelector('#extensionsMenu') ||
                              document.querySelector('#extensionsMenu');
            }

            if (!extensionMenu) {
                // 如果仍然没有找到，创建一个简单的菜单容器
                extensionMenu = document.createElement('div');
                extensionMenu.className = 'dropdown-menu';
                extensionMenu.id = 'extensionsMenu';
                extensionContainer.parentElement.appendChild(extensionMenu);
            }

            // 创建"信息栏设置"菜单项
            const settingsMenuItem = document.createElement('a');
            settingsMenuItem.id = 'infobar-settings-menu-item';
            settingsMenuItem.className = 'dropdown-item';
            settingsMenuItem.href = '#';
            settingsMenuItem.innerHTML = '<i class="fa-solid fa-cog"></i> 信息栏设置';

            // 创建"数据表格"菜单项
            const tableMenuItem = document.createElement('a');
            tableMenuItem.id = 'infobar-table-menu-item';
            tableMenuItem.className = 'dropdown-item';
            tableMenuItem.href = '#';
            tableMenuItem.innerHTML = '<i class="fa-solid fa-table"></i> 数据表格';

            // 绑定菜单项事件
            settingsMenuItem.addEventListener('click', (e) => {
                e.preventDefault();
                this.infoBarSettings.show();
            });

            tableMenuItem.addEventListener('click', (e) => {
                e.preventDefault();
                this.dataTable.show();
            });

            // 添加分隔线（如果菜单中已有其他项目）
            if (extensionMenu.children.length > 0) {
                const separator = document.createElement('div');
                separator.className = 'dropdown-divider';
                extensionMenu.appendChild(separator);
            }

            // 添加菜单项到扩展菜单
            extensionMenu.appendChild(settingsMenuItem);
            extensionMenu.appendChild(tableMenuItem);
            
            console.log('[InfoBarTool] ✅ 用户界面创建完成');
            
        } catch (error) {
            console.error('[InfoBarTool] ❌ 创建用户界面失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 错误处理
     */
    handleError(error) {
        this.errorCount++;
        console.error(`[InfoBarTool] ❌ 错误 #${this.errorCount}:`, error);
        
        // 触发错误事件
        if (this.eventSystem) {
            this.eventSystem.emit('tool:error', {
                error: error.message,
                count: this.errorCount,
                timestamp: Date.now()
            });
        }
        
        // 如果错误过多，禁用扩展
        if (this.errorCount >= 5) {
            console.error('[InfoBarTool] ⚠️ 错误过多，禁用扩展');
            this.disable();
        }
    }

    /**
     * 禁用扩展
     */
    disable() {
        this.initialized = false;
        console.log('[InfoBarTool] 🚫 扩展已禁用');
    }

    /**
     * 获取扩展状态
     */
    getStatus() {
        return {
            initialized: this.initialized,
            errorCount: this.errorCount,
            version: this.VERSION,
            modules: {
                dataCore: !!this.dataCore,
                eventSystem: !!this.eventSystem,
                configManager: !!this.configManager,
                apiIntegration: !!this.apiIntegration,
                infoBarSettings: !!this.infoBarSettings,
                dataTable: !!this.dataTable
            }
        };
    }
}

// 创建全局实例
const informationBarTool = new InformationBarIntegrationTool();

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', informationBarTool.init);
} else {
    informationBarTool.init();
}

// 导出到全局作用域
window.SillyTavernInfobar = informationBarTool;

console.log('[InfoBarTool] 📦 Information Bar Integration Tool 加载完成');
