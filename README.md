# Information Bar Integration Tool

SillyTavern第三方扩展插件 - 信息栏集成工具

## 项目概述

这是一个为SillyTavern开发的第三方扩展插件，提供强大的信息栏集成功能，包括：

- 📊 信息栏设置界面
- 📋 数据表格管理
- 🔧 自定义API配置
- 💾 智能数据管理
- 🎨 界面定制功能

## 技术架构

- **开发语言**: JavaScript ES6+
- **架构模式**: 模块化架构，事件驱动
- **数据存储**: localStorage + chatMetadata
- **界面位置**: SillyTavern左下角扩展区域

## 文件结构

```
Information bar integration tool/
├── manifest.json                 # 扩展配置文件
├── index.js                     # 主入口文件
├── style.css                    # 主样式文件
├── core/                        # 核心模块目录
├── ui/                          # UI组件目录
├── modules/                     # 功能模块目录
├── styles/                      # 样式文件目录
├── assets/                      # 资源文件目录
└── utils/                       # 工具函数目录
```

## 开发状态

🚧 项目正在开发中...

## 版本信息

- **当前版本**: 1.0.0
- **开发日期**: 2025-01-28
- **兼容性**: SillyTavern 最新版本

## 许可证

AGPLv3 License
