/**
 * 数据表格界面
 * 
 * 负责管理数据表格的显示和交互：
 * - 表格数据的显示和渲染
 * - 数据筛选和搜索功能
 * - 分页和排序功能
 * - 数据导入导出功能
 * - 表格配置和自定义
 * 
 * @class DataTable
 */

export class DataTable {
    constructor(dataCore, configManager, eventSystem) {
        console.log('[DataTable] 🔧 数据表格界面初始化开始');
        
        this.dataCore = dataCore;
        this.configManager = configManager;
        this.eventSystem = eventSystem;
        
        // UI元素引用
        this.container = null;
        this.modal = null;
        this.table = null;
        this.toolbar = null;
        
        // 表格数据
        this.data = [];
        this.filteredData = [];
        this.columns = [];
        
        // 分页设置
        this.pagination = {
            currentPage: 1,
            pageSize: 20,
            totalPages: 0,
            totalItems: 0
        };
        
        // 排序设置
        this.sorting = {
            column: null,
            direction: 'asc' // 'asc' | 'desc'
        };
        
        // 筛选设置
        this.filters = {
            search: '',
            dateRange: { start: null, end: null },
            category: '',
            status: ''
        };
        
        // 选中的行
        this.selectedRows = new Set();
        
        // 初始化状态
        this.initialized = false;
        this.visible = false;
        this.errorCount = 0;
        
        // 绑定方法
        this.init = this.init.bind(this);
        this.show = this.show.bind(this);
        this.hide = this.hide.bind(this);
        this.loadData = this.loadData.bind(this);
        this.renderTable = this.renderTable.bind(this);
        this.applyFilters = this.applyFilters.bind(this);
    }

    /**
     * 初始化数据表格界面
     */
    async init() {
        try {
            console.log('[DataTable] 📊 开始初始化数据表格界面...');
            
            if (!this.dataCore) {
                throw new Error('数据核心未初始化');
            }
            
            // 创建UI
            this.createUI();
            
            // 初始化表格列配置
            this.initColumns();
            
            // 加载数据
            await this.loadData();
            
            // 绑定事件
            this.bindEvents();
            
            this.initialized = true;
            console.log('[DataTable] ✅ 数据表格界面初始化完成');
            
        } catch (error) {
            console.error('[DataTable] ❌ 初始化失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 创建UI界面
     */
    createUI() {
        try {
            // 创建新的深色主题数据表格界面
            this.modal = document.createElement('div');
            this.modal.id = 'data-table-modal';
            this.modal.className = 'data-table-modal datatable-modal-new';
            this.modal.style.display = 'none';

            this.modal.innerHTML = `
                <div class="modal-overlay" onclick="this.closest('.data-table-modal').style.display='none'"></div>
                <div class="modal-container">
                    <!-- 顶部标题栏 -->
                    <div class="modal-header">
                        <div class="header-left">
                            <div class="header-icon">📊</div>
                            <h2>数据表格</h2>
                        </div>
                        <div class="header-right">
                            <div class="success-notification" style="display: block;">
                                <span class="success-icon">✅</span>
                                <span class="success-text">数据表格已成功加载！</span>
                            </div>
                            <button class="modal-close" onclick="this.closest('.data-table-modal').style.display='none'">×</button>
                        </div>
                    </div>

                    <!-- 顶部工具栏 -->
                    <div class="table-toolbar">
                        <div class="toolbar-left">
                            <button class="btn-tool active" data-action="table-records">
                                <span class="tool-icon">📋</span>
                                <span class="tool-text">表格记录</span>
                            </button>
                            <button class="btn-tool" data-action="lock-data">
                                <span class="tool-icon">🔒</span>
                                <span class="tool-text">锁定数据</span>
                            </button>
                            <button class="btn-tool" data-action="export-data">
                                <span class="tool-icon">📤</span>
                                <span class="tool-text">导出数据</span>
                            </button>
                            <button class="btn-tool" data-action="import-data">
                                <span class="tool-icon">📥</span>
                                <span class="tool-text">导入数据</span>
                            </button>
                            <button class="btn-tool" data-action="backup-data">
                                <span class="tool-icon">💾</span>
                                <span class="tool-text">备份数据</span>
                            </button>
                        </div>
                        <div class="toolbar-right">
                            <div class="search-box">
                                <input type="text" placeholder="搜索数据..." class="search-input" />
                                <button class="btn-search">🔍</button>
                            </div>
                            <button class="btn-tool" data-action="refresh">
                                <span class="tool-icon">🔄</span>
                                <span class="tool-text">刷新</span>
                            </button>
                        </div>
                    </div>

                    <!-- 主体内容区域 -->
                    <div class="modal-body">
                        ${this.createGroupedTables()}
                    </div>

                    <!-- 底部状态栏 -->
                    <div class="modal-footer">
                        <div class="footer-left">
                            <span class="status-text">数据加载完成</span>
                            <span class="record-count">共 <span class="count-number">0</span> 条记录</span>
                        </div>
                        <div class="footer-right">
                            <button class="btn-pagination" data-action="prev-page">上一页</button>
                            <span class="page-info">第 <span class="current-page">1</span> 页，共 <span class="total-pages">1</span> 页</span>
                            <button class="btn-pagination" data-action="next-page">下一页</button>
                        </div>
                    </div>
                </div>
            `;

            // 添加到页面
            document.body.appendChild(this.modal);

            // 获取关键元素引用
            this.toolbar = this.modal.querySelector('.table-toolbar');
            this.tableContainer = this.modal.querySelector('.modal-body');

            // 绑定新的事件处理
            this.bindNewEvents();

            console.log('[DataTable] 🎨 新UI界面创建完成');

        } catch (error) {
            console.error('[DataTable] ❌ 创建UI失败:', error);
            throw error;
        }
    }

    /**
     * 创建分组表格
     */
    createGroupedTables() {
        return `
            <div class="grouped-tables">
                <!-- 个人信息组 -->
                <div class="table-group">
                    <div class="group-header">
                        <div class="group-title">
                            <span class="group-icon">👤</span>
                            <span class="group-name">个人信息</span>
                            <span class="group-count">(15 项)</span>
                        </div>
                        <div class="group-actions">
                            <button class="btn-group-action" data-action="expand-group" data-group="personal">
                                <span class="expand-icon">▼</span>
                            </button>
                            <button class="btn-group-action" data-action="edit-group" data-group="personal">
                                <span class="edit-icon">✏️</span>
                            </button>
                        </div>
                    </div>
                    <div class="group-content expanded">
                        ${this.createPersonalInfoTable()}
                    </div>
                </div>

                <!-- 世界信息组 -->
                <div class="table-group">
                    <div class="group-header">
                        <div class="group-title">
                            <span class="group-icon">🌍</span>
                            <span class="group-name">世界信息</span>
                            <span class="group-count">(8 项)</span>
                        </div>
                        <div class="group-actions">
                            <button class="btn-group-action" data-action="expand-group" data-group="world">
                                <span class="expand-icon">▼</span>
                            </button>
                            <button class="btn-group-action" data-action="edit-group" data-group="world">
                                <span class="edit-icon">✏️</span>
                            </button>
                        </div>
                    </div>
                    <div class="group-content expanded">
                        ${this.createWorldInfoTable()}
                    </div>
                </div>

                <!-- 交互对象组 -->
                <div class="table-group">
                    <div class="group-header">
                        <div class="group-title">
                            <span class="group-icon">👥</span>
                            <span class="group-name">交互对象</span>
                            <span class="group-count">(12 项)</span>
                        </div>
                        <div class="group-actions">
                            <button class="btn-group-action" data-action="expand-group" data-group="interaction">
                                <span class="expand-icon">▼</span>
                            </button>
                            <button class="btn-group-action" data-action="edit-group" data-group="interaction">
                                <span class="edit-icon">✏️</span>
                            </button>
                        </div>
                    </div>
                    <div class="group-content expanded">
                        ${this.createInteractionTable()}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建个人信息表格
     */
    createPersonalInfoTable() {
        return `
            <div class="data-table-container">
                <table class="data-table dark-table">
                    <thead>
                        <tr>
                            <th class="col-checkbox">
                                <input type="checkbox" class="select-all-checkbox" />
                            </th>
                            <th class="col-name">属性名称</th>
                            <th class="col-value">当前值</th>
                            <th class="col-type">数据类型</th>
                            <th class="col-status">状态</th>
                            <th class="col-modified">最后修改</th>
                            <th class="col-actions">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="data-row">
                            <td><input type="checkbox" class="row-checkbox" /></td>
                            <td class="cell-name">姓名</td>
                            <td class="cell-value">林天</td>
                            <td class="cell-type">文本</td>
                            <td class="cell-status"><span class="status-badge active">已启用</span></td>
                            <td class="cell-modified">2024-01-15 14:30</td>
                            <td class="cell-actions">
                                <button class="btn-action edit" title="编辑">✏️</button>
                                <button class="btn-action delete" title="删除">🗑️</button>
                            </td>
                        </tr>
                        <tr class="data-row">
                            <td><input type="checkbox" class="row-checkbox" /></td>
                            <td class="cell-name">年龄</td>
                            <td class="cell-value">25</td>
                            <td class="cell-type">数字</td>
                            <td class="cell-status"><span class="status-badge active">已启用</span></td>
                            <td class="cell-modified">2024-01-15 14:25</td>
                            <td class="cell-actions">
                                <button class="btn-action edit" title="编辑">✏️</button>
                                <button class="btn-action delete" title="删除">🗑️</button>
                            </td>
                        </tr>
                        <tr class="data-row">
                            <td><input type="checkbox" class="row-checkbox" /></td>
                            <td class="cell-name">职业</td>
                            <td class="cell-value">软件工程师</td>
                            <td class="cell-type">文本</td>
                            <td class="cell-status"><span class="status-badge inactive">未启用</span></td>
                            <td class="cell-modified">2024-01-14 16:45</td>
                            <td class="cell-actions">
                                <button class="btn-action edit" title="编辑">✏️</button>
                                <button class="btn-action delete" title="删除">🗑️</button>
                            </td>
                        </tr>
                        <tr class="data-row">
                            <td><input type="checkbox" class="row-checkbox" /></td>
                            <td class="cell-name">性格</td>
                            <td class="cell-value">开朗、友善</td>
                            <td class="cell-type">文本</td>
                            <td class="cell-status"><span class="status-badge active">已启用</span></td>
                            <td class="cell-modified">2024-01-15 10:20</td>
                            <td class="cell-actions">
                                <button class="btn-action edit" title="编辑">✏️</button>
                                <button class="btn-action delete" title="删除">🗑️</button>
                            </td>
                        </tr>
                        <tr class="data-row">
                            <td><input type="checkbox" class="row-checkbox" /></td>
                            <td class="cell-name">爱好</td>
                            <td class="cell-value">编程、阅读、音乐</td>
                            <td class="cell-type">列表</td>
                            <td class="cell-status"><span class="status-badge active">已启用</span></td>
                            <td class="cell-modified">2024-01-13 09:15</td>
                            <td class="cell-actions">
                                <button class="btn-action edit" title="编辑">✏️</button>
                                <button class="btn-action delete" title="删除">🗑️</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * 创建世界信息表格
     */
    createWorldInfoTable() {
        return `
            <div class="data-table-container">
                <table class="data-table dark-table">
                    <thead>
                        <tr>
                            <th class="col-checkbox">
                                <input type="checkbox" class="select-all-checkbox" />
                            </th>
                            <th class="col-name">世界属性</th>
                            <th class="col-value">当前值</th>
                            <th class="col-type">数据类型</th>
                            <th class="col-status">状态</th>
                            <th class="col-modified">最后修改</th>
                            <th class="col-actions">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="data-row">
                            <td><input type="checkbox" class="row-checkbox" /></td>
                            <td class="cell-name">世界名称</td>
                            <td class="cell-value">现代都市</td>
                            <td class="cell-type">文本</td>
                            <td class="cell-status"><span class="status-badge active">已启用</span></td>
                            <td class="cell-modified">2024-01-15 12:00</td>
                            <td class="cell-actions">
                                <button class="btn-action edit" title="编辑">✏️</button>
                                <button class="btn-action delete" title="删除">🗑️</button>
                            </td>
                        </tr>
                        <tr class="data-row">
                            <td><input type="checkbox" class="row-checkbox" /></td>
                            <td class="cell-name">时代背景</td>
                            <td class="cell-value">21世纪</td>
                            <td class="cell-type">文本</td>
                            <td class="cell-status"><span class="status-badge active">已启用</span></td>
                            <td class="cell-modified">2024-01-15 11:45</td>
                            <td class="cell-actions">
                                <button class="btn-action edit" title="编辑">✏️</button>
                                <button class="btn-action delete" title="删除">🗑️</button>
                            </td>
                        </tr>
                        <tr class="data-row">
                            <td><input type="checkbox" class="row-checkbox" /></td>
                            <td class="cell-name">地理位置</td>
                            <td class="cell-value">中国上海</td>
                            <td class="cell-type">文本</td>
                            <td class="cell-status"><span class="status-badge active">已启用</span></td>
                            <td class="cell-modified">2024-01-14 18:30</td>
                            <td class="cell-actions">
                                <button class="btn-action edit" title="编辑">✏️</button>
                                <button class="btn-action delete" title="删除">🗑️</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * 创建交互对象表格
     */
    createInteractionTable() {
        return `
            <div class="data-table-container">
                <table class="data-table dark-table">
                    <thead>
                        <tr>
                            <th class="col-checkbox">
                                <input type="checkbox" class="select-all-checkbox" />
                            </th>
                            <th class="col-name">对象名称</th>
                            <th class="col-value">关系类型</th>
                            <th class="col-type">互动频率</th>
                            <th class="col-status">状态</th>
                            <th class="col-modified">最后互动</th>
                            <th class="col-actions">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="data-row">
                            <td><input type="checkbox" class="row-checkbox" /></td>
                            <td class="cell-name">小雨</td>
                            <td class="cell-value">朋友</td>
                            <td class="cell-type">经常</td>
                            <td class="cell-status"><span class="status-badge active">在线</span></td>
                            <td class="cell-modified">2024-01-15 16:20</td>
                            <td class="cell-actions">
                                <button class="btn-action edit" title="编辑">✏️</button>
                                <button class="btn-action delete" title="删除">🗑️</button>
                            </td>
                        </tr>
                        <tr class="data-row">
                            <td><input type="checkbox" class="row-checkbox" /></td>
                            <td class="cell-name">张经理</td>
                            <td class="cell-value">同事</td>
                            <td class="cell-type">偶尔</td>
                            <td class="cell-status"><span class="status-badge inactive">离线</span></td>
                            <td class="cell-modified">2024-01-14 09:30</td>
                            <td class="cell-actions">
                                <button class="btn-action edit" title="编辑">✏️</button>
                                <button class="btn-action delete" title="删除">🗑️</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }



    /**
     * 绑定新的事件处理
     */
    bindNewEvents() {
        if (!this.modal) return;

        // 工具栏按钮事件（包括展开/收起）
        this.modal.addEventListener('click', (e) => {
            const actionElement = e.target.closest('[data-action]');
            if (actionElement) {
                e.preventDefault();
                e.stopPropagation();
                const action = actionElement.getAttribute('data-action');
                this.handleToolbarAction(action, e);
            }
        });

        // 复选框事件
        this.modal.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox') {
                this.handleCheckboxChange(e);
            }
        });
    }

    /**
     * 处理工具栏操作
     */
    handleToolbarAction(action, event) {
        console.log('[DataTable] 工具栏操作:', action);

        switch (action) {
            case 'table-records':
                this.showTableRecords();
                break;
            case 'lock-data':
                this.toggleDataLock();
                break;
            case 'export-data':
                this.exportData();
                break;
            case 'import-data':
                this.importData();
                break;
            case 'backup-data':
                this.backupData();
                break;
            case 'refresh':
                this.refreshData();
                break;
            case 'expand-group':
                event.preventDefault();
                event.stopPropagation();
                const groupName = event.target.closest('[data-group]')?.getAttribute('data-group');
                if (groupName) {
                    this.toggleGroup(groupName);
                }
                break;
            case 'edit-group':
                const editGroupName = event.target.closest('[data-group]')?.getAttribute('data-group');
                if (editGroupName) {
                    this.editGroup(editGroupName);
                }
                break;
            default:
                console.log('[DataTable] 未知操作:', action);
        }
    }

    /**
     * 切换分组显示状态
     */
    toggleGroup(groupName) {
        // 防抖机制，避免重复触发
        const now = Date.now();
        const lastToggle = this.lastToggleTime || 0;
        if (now - lastToggle < 100) {
            console.log(`[DataTable] ⏸️ 防抖跳过: ${groupName}`);
            return;
        }
        this.lastToggleTime = now;

        const group = this.modal.querySelector(`[data-group="${groupName}"]`).closest('.table-group');
        const content = group.querySelector('.group-content');
        const expandIcon = group.querySelector('.expand-icon');

        if (content.classList.contains('expanded')) {
            content.classList.remove('expanded');
            expandIcon.textContent = '▶';
            console.log(`[DataTable] 📁 收起分组: ${groupName}`);
        } else {
            content.classList.add('expanded');
            expandIcon.textContent = '▼';
            console.log(`[DataTable] 📂 展开分组: ${groupName}`);
        }
    }

    /**
     * 编辑分组
     */
    editGroup(groupName) {
        console.log(`[DataTable] ✏️ 编辑分组: ${groupName}`);
        // 这里可以添加编辑分组的逻辑
        this.showMessage(`编辑分组: ${groupName}`, 'info');
    }

    /**
     * 处理复选框变更
     */
    handleCheckboxChange(event) {
        const checkbox = event.target;

        if (checkbox.classList.contains('select-all-checkbox')) {
            // 全选/取消全选
            const table = checkbox.closest('table');
            const rowCheckboxes = table.querySelectorAll('.row-checkbox');
            rowCheckboxes.forEach(cb => cb.checked = checkbox.checked);
        }

        this.updateSelectionCount();
    }

    /**
     * 更新选择计数
     */
    updateSelectionCount() {
        const selectedCount = this.modal.querySelectorAll('.row-checkbox:checked').length;
        const countElement = this.modal.querySelector('.count-number');
        if (countElement) {
            countElement.textContent = selectedCount;
        }
    }

    // 工具栏操作方法的占位符实现
    showTableRecords() { console.log('[DataTable] 显示表格记录'); }
    toggleDataLock() { console.log('[DataTable] 切换数据锁定'); }
    exportData() { console.log('[DataTable] 导出数据'); }
    importData() { console.log('[DataTable] 导入数据'); }
    backupData() { console.log('[DataTable] 备份数据'); }
    refreshData() { console.log('[DataTable] 刷新数据'); }

    /**
     * 创建表格容器
     */
    createTableContainer() {
        return `
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr class="table-header">
                            <th class="select-column">
                                <input type="checkbox" class="select-all-checkbox" />
                            </th>
                            <!-- 动态生成列头 -->
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        <!-- 动态生成数据行 -->
                    </tbody>
                </table>
                
                <div class="table-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <span>加载中...</span>
                </div>
                
                <div class="table-empty" style="display: none;">
                    <div class="empty-icon">📊</div>
                    <h3>暂无数据</h3>
                    <p>还没有任何数据记录</p>
                    <button class="btn btn-primary" data-action="add-data">添加数据</button>
                </div>
            </div>
        `;
    }

    /**
     * 创建分页组件
     */
    createPagination() {
        return `
            <div class="table-pagination">
                <div class="pagination-info">
                    <span>显示 <span class="current-range">1-20</span> 条，共 <span class="total-items">0</span> 条</span>
                    
                    <select class="page-size-select">
                        <option value="10">10条/页</option>
                        <option value="20" selected>20条/页</option>
                        <option value="50">50条/页</option>
                        <option value="100">100条/页</option>
                    </select>
                </div>
                
                <div class="pagination-controls">
                    <button class="btn btn-small" data-action="first-page" disabled>首页</button>
                    <button class="btn btn-small" data-action="prev-page" disabled>上一页</button>
                    
                    <div class="page-numbers">
                        <!-- 动态生成页码 -->
                    </div>
                    
                    <button class="btn btn-small" data-action="next-page" disabled>下一页</button>
                    <button class="btn btn-small" data-action="last-page" disabled>末页</button>
                </div>
            </div>
        `;
    }

    /**
     * 初始化表格列配置
     */
    initColumns() {
        this.columns = [
            {
                key: 'id',
                title: 'ID',
                width: '80px',
                sortable: true,
                type: 'text'
            },
            {
                key: 'timestamp',
                title: '时间',
                width: '150px',
                sortable: true,
                type: 'datetime',
                formatter: (value) => new Date(value).toLocaleString()
            },
            {
                key: 'category',
                title: '分类',
                width: '100px',
                sortable: true,
                type: 'text',
                formatter: (value) => {
                    const categoryMap = {
                        'character': '角色信息',
                        'chat': '聊天记录',
                        'system': '系统数据'
                    };
                    return categoryMap[value] || value;
                }
            },
            {
                key: 'title',
                title: '标题',
                width: '200px',
                sortable: true,
                type: 'text'
            },
            {
                key: 'content',
                title: '内容',
                width: '300px',
                sortable: false,
                type: 'text',
                formatter: (value) => {
                    if (typeof value === 'object') {
                        return JSON.stringify(value).substring(0, 100) + '...';
                    }
                    return String(value).substring(0, 100) + (value.length > 100 ? '...' : '');
                }
            },
            {
                key: 'status',
                title: '状态',
                width: '80px',
                sortable: true,
                type: 'status',
                formatter: (value) => {
                    const statusMap = {
                        'active': '<span class="status-badge status-active">活跃</span>',
                        'inactive': '<span class="status-badge status-inactive">非活跃</span>',
                        'archived': '<span class="status-badge status-archived">已归档</span>'
                    };
                    return statusMap[value] || value;
                }
            },
            {
                key: 'actions',
                title: '操作',
                width: '120px',
                sortable: false,
                type: 'actions',
                formatter: (value, row) => {
                    return `
                        <div class="action-buttons">
                            <button class="btn btn-small" data-action="view" data-id="${row.id}">查看</button>
                            <button class="btn btn-small" data-action="edit" data-id="${row.id}">编辑</button>
                            <button class="btn btn-small btn-danger" data-action="delete" data-id="${row.id}">删除</button>
                        </div>
                    `;
                }
            }
        ];
    }

    /**
     * 加载数据
     */
    async loadData() {
        try {
            console.log('[DataTable] 📥 开始加载数据...');

            if (!this.dataCore) {
                throw new Error('数据核心未初始化');
            }

            this.showLoading(true);

            // 从数据核心获取所有数据
            const allData = await this.dataCore.getAllData('all');
            
            // 转换数据格式
            this.data = this.transformData(allData);
            
            // 应用筛选
            this.applyFilters();
            
            // 渲染表格
            this.renderTable();
            
            this.showLoading(false);
            
            console.log(`[DataTable] ✅ 数据加载完成，共 ${this.data.length} 条记录`);
            
        } catch (error) {
            console.error('[DataTable] ❌ 加载数据失败:', error);
            this.showLoading(false);
            this.showEmpty(true);
            this.handleError(error);
        }
    }

    /**
     * 转换数据格式
     */
    transformData(rawData) {
        const transformedData = [];
        let idCounter = 1;
        
        // 处理全局数据
        if (rawData.global) {
            for (const [key, value] of Object.entries(rawData.global)) {
                transformedData.push({
                    id: idCounter++,
                    timestamp: Date.now(),
                    category: 'system',
                    title: key,
                    content: value,
                    status: 'active',
                    source: 'global'
                });
            }
        }
        
        // 处理聊天数据
        if (rawData.chat) {
            for (const [key, value] of Object.entries(rawData.chat)) {
                transformedData.push({
                    id: idCounter++,
                    timestamp: Date.now(),
                    category: 'chat',
                    title: key,
                    content: value,
                    status: 'active',
                    source: 'chat'
                });
            }
        }
        
        return transformedData;
    }

    /**
     * 应用筛选
     */
    applyFilters() {
        try {
            let filtered = [...this.data];
            
            // 搜索筛选
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                filtered = filtered.filter(item => 
                    item.title.toLowerCase().includes(searchTerm) ||
                    String(item.content).toLowerCase().includes(searchTerm)
                );
            }
            
            // 分类筛选
            if (this.filters.category) {
                filtered = filtered.filter(item => item.category === this.filters.category);
            }
            
            // 状态筛选
            if (this.filters.status) {
                filtered = filtered.filter(item => item.status === this.filters.status);
            }
            
            // 日期范围筛选
            if (this.filters.dateRange.start) {
                const startDate = new Date(this.filters.dateRange.start).getTime();
                filtered = filtered.filter(item => item.timestamp >= startDate);
            }
            
            if (this.filters.dateRange.end) {
                const endDate = new Date(this.filters.dateRange.end).getTime() + 24 * 60 * 60 * 1000; // 包含结束日期
                filtered = filtered.filter(item => item.timestamp <= endDate);
            }
            
            // 排序
            if (this.sorting.column) {
                filtered.sort((a, b) => {
                    const aValue = a[this.sorting.column];
                    const bValue = b[this.sorting.column];
                    
                    let comparison = 0;
                    if (aValue < bValue) comparison = -1;
                    if (aValue > bValue) comparison = 1;
                    
                    return this.sorting.direction === 'desc' ? -comparison : comparison;
                });
            }
            
            this.filteredData = filtered;
            
            // 更新分页信息
            this.updatePagination();
            
            console.log(`[DataTable] 🔍 筛选完成，显示 ${filtered.length} 条记录`);
            
        } catch (error) {
            console.error('[DataTable] ❌ 应用筛选失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 更新分页信息
     */
    updatePagination() {
        this.pagination.totalItems = this.filteredData.length;
        this.pagination.totalPages = Math.ceil(this.pagination.totalItems / this.pagination.pageSize);
        
        // 确保当前页在有效范围内
        if (this.pagination.currentPage > this.pagination.totalPages) {
            this.pagination.currentPage = Math.max(1, this.pagination.totalPages);
        }
    }

    /**
     * 渲染表格
     */
    renderTable() {
        try {
            if (this.filteredData.length === 0) {
                this.showEmpty(true);
                return;
            }
            
            this.showEmpty(false);

            // 新的界面已经在createUI中渲染完成，这里只需要更新数据
            this.updateTableData();

            console.log('[DataTable] 🎨 表格渲染完成');
            
        } catch (error) {
            console.error('[DataTable] ❌ 渲染表格失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 更新表格数据
     */
    updateTableData() {
        if (!this.modal) return;

        // 更新记录计数
        const countElement = this.modal.querySelector('.count-number');
        if (countElement) {
            const totalRecords = this.data ? this.data.length : 0;
            countElement.textContent = totalRecords;
        }

        // 更新状态文本
        const statusText = this.modal.querySelector('.status-text');
        if (statusText) {
            statusText.textContent = '数据加载完成';
        }

        console.log('[DataTable] 📊 表格数据已更新');
    }

    /**
     * 渲染表头
     */
    renderTableHeader() {
        const headerRow = this.table.querySelector('.table-header');
        
        // 清空现有列头（保留选择列）
        const selectColumn = headerRow.querySelector('.select-column');
        headerRow.innerHTML = '';
        headerRow.appendChild(selectColumn);
        
        // 添加数据列头
        this.columns.forEach(column => {
            const th = document.createElement('th');
            th.className = 'table-column';
            th.style.width = column.width;
            th.dataset.column = column.key;
            
            let headerContent = column.title;
            
            // 添加排序指示器
            if (column.sortable) {
                th.classList.add('sortable');
                const sortIcon = this.sorting.column === column.key 
                    ? (this.sorting.direction === 'asc' ? '↑' : '↓')
                    : '↕';
                headerContent += ` <span class="sort-icon">${sortIcon}</span>`;
            }
            
            th.innerHTML = headerContent;
            headerRow.appendChild(th);
        });
    }

    /**
     * 渲染表体
     */
    renderTableBody() {
        const tbody = this.table.querySelector('.table-body');
        tbody.innerHTML = '';
        
        // 计算当前页的数据范围
        const startIndex = (this.pagination.currentPage - 1) * this.pagination.pageSize;
        const endIndex = Math.min(startIndex + this.pagination.pageSize, this.filteredData.length);
        const pageData = this.filteredData.slice(startIndex, endIndex);
        
        // 渲染数据行
        pageData.forEach(row => {
            const tr = document.createElement('tr');
            tr.className = 'table-row';
            tr.dataset.id = row.id;
            
            // 选择列
            const selectTd = document.createElement('td');
            selectTd.className = 'select-column';
            selectTd.innerHTML = `<input type="checkbox" class="row-checkbox" value="${row.id}" />`;
            tr.appendChild(selectTd);
            
            // 数据列
            this.columns.forEach(column => {
                const td = document.createElement('td');
                td.className = 'table-cell';
                td.dataset.column = column.key;
                
                let cellContent = row[column.key];
                
                // 应用格式化器
                if (column.formatter) {
                    cellContent = column.formatter(cellContent, row);
                }
                
                // 处理HTML内容
                if (column.type === 'status' || column.type === 'actions') {
                    td.innerHTML = cellContent;
                } else {
                    td.textContent = cellContent || '';
                }
                
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
    }

    /**
     * 渲染分页
     */
    renderPagination() {
        // 更新分页信息
        const currentRangeEl = this.modal.querySelector('.current-range');
        const totalItemsEl = this.modal.querySelector('.total-items');
        
        const startIndex = (this.pagination.currentPage - 1) * this.pagination.pageSize + 1;
        const endIndex = Math.min(this.pagination.currentPage * this.pagination.pageSize, this.pagination.totalItems);
        
        currentRangeEl.textContent = `${startIndex}-${endIndex}`;
        totalItemsEl.textContent = this.pagination.totalItems;
        
        // 更新分页按钮状态
        const firstBtn = this.modal.querySelector('[data-action="first-page"]');
        const prevBtn = this.modal.querySelector('[data-action="prev-page"]');
        const nextBtn = this.modal.querySelector('[data-action="next-page"]');
        const lastBtn = this.modal.querySelector('[data-action="last-page"]');
        
        firstBtn.disabled = prevBtn.disabled = this.pagination.currentPage <= 1;
        nextBtn.disabled = lastBtn.disabled = this.pagination.currentPage >= this.pagination.totalPages;
        
        // 渲染页码
        this.renderPageNumbers();
    }

    /**
     * 渲染页码
     */
    renderPageNumbers() {
        const pageNumbersContainer = this.modal.querySelector('.page-numbers');
        pageNumbersContainer.innerHTML = '';
        
        const { currentPage, totalPages } = this.pagination;
        
        // 计算显示的页码范围
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);
        
        // 调整范围以显示5个页码
        if (endPage - startPage < 4) {
            if (startPage === 1) {
                endPage = Math.min(totalPages, startPage + 4);
            } else {
                startPage = Math.max(1, endPage - 4);
            }
        }
        
        // 渲染页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `btn btn-small page-btn ${i === currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.dataset.action = 'goto-page';
            pageBtn.dataset.page = i;
            
            pageNumbersContainer.appendChild(pageBtn);
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        try {
            // 模态框事件
            this.modal.addEventListener('click', (e) => {
                this.handleClick(e);
            });
            
            // 搜索事件
            const searchInput = this.modal.querySelector('.search-input');
            searchInput.addEventListener('input', this.debounce((e) => {
                this.filters.search = e.target.value;
                this.applyFilters();
                this.renderTable();
            }, 300));
            
            // 筛选事件
            this.modal.addEventListener('change', (e) => {
                this.handleFilterChange(e);
            });
            
            // 表格排序事件 - 使用新的事件处理
            this.bindNewEvents();
            
            console.log('[DataTable] 🔗 事件绑定完成');
            
        } catch (error) {
            console.error('[DataTable] ❌ 绑定事件失败:', error);
            throw error;
        }
    }

    /**
     * 处理点击事件
     */
    handleClick(e) {
        const action = e.target.dataset.action;
        
        switch (action) {
            case 'close':
                this.hide();
                break;
            case 'refresh':
                this.loadData();
                break;
            case 'export':
                this.exportData();
                break;
            case 'import':
                this.importData();
                break;
            case 'search':
                this.performSearch();
                break;
            case 'clear-filters':
                this.clearFilters();
                break;
            case 'select-all':
                this.selectAll();
                break;
            case 'delete-selected':
                this.deleteSelected();
                break;
            case 'export-selected':
                this.exportSelected();
                break;
            case 'first-page':
                this.goToPage(1);
                break;
            case 'prev-page':
                this.goToPage(this.pagination.currentPage - 1);
                break;
            case 'next-page':
                this.goToPage(this.pagination.currentPage + 1);
                break;
            case 'last-page':
                this.goToPage(this.pagination.totalPages);
                break;
            case 'goto-page':
                this.goToPage(parseInt(e.target.dataset.page));
                break;
            case 'view':
                this.viewItem(e.target.dataset.id);
                break;
            case 'edit':
                this.editItem(e.target.dataset.id);
                break;
            case 'delete':
                this.deleteItem(e.target.dataset.id);
                break;
        }
    }

    /**
     * 处理筛选变更
     */
    handleFilterChange(e) {
        const filterType = e.target.dataset.filter;
        
        if (filterType) {
            switch (filterType) {
                case 'category':
                    this.filters.category = e.target.value;
                    break;
                case 'status':
                    this.filters.status = e.target.value;
                    break;
                case 'dateStart':
                    this.filters.dateRange.start = e.target.value;
                    break;
                case 'dateEnd':
                    this.filters.dateRange.end = e.target.value;
                    break;
            }
            
            this.applyFilters();
            this.renderTable();
        }
        
        // 页面大小变更
        if (e.target.classList.contains('page-size-select')) {
            this.pagination.pageSize = parseInt(e.target.value);
            this.pagination.currentPage = 1;
            this.applyFilters();
            this.renderTable();
        }
        
        // 行选择变更
        if (e.target.classList.contains('row-checkbox')) {
            const rowId = e.target.value;
            if (e.target.checked) {
                this.selectedRows.add(rowId);
            } else {
                this.selectedRows.delete(rowId);
            }
            this.updateBatchActions();
        }
        
        // 全选变更
        if (e.target.classList.contains('select-all-checkbox')) {
            this.toggleSelectAll(e.target.checked);
        }
    }

    /**
     * 处理排序
     */
    handleSort(th) {
        const column = th.dataset.column;
        
        if (this.sorting.column === column) {
            // 切换排序方向
            this.sorting.direction = this.sorting.direction === 'asc' ? 'desc' : 'asc';
        } else {
            // 新的排序列
            this.sorting.column = column;
            this.sorting.direction = 'asc';
        }
        
        this.applyFilters();
        this.renderTable();
    }

    /**
     * 跳转到指定页
     */
    goToPage(page) {
        if (page >= 1 && page <= this.pagination.totalPages) {
            this.pagination.currentPage = page;
            this.renderTable();
        }
    }

    /**
     * 清除筛选
     */
    clearFilters() {
        this.filters = {
            search: '',
            dateRange: { start: null, end: null },
            category: '',
            status: ''
        };
        
        // 重置表单
        this.modal.querySelector('.search-input').value = '';
        this.modal.querySelectorAll('.filter-select').forEach(select => select.value = '');
        this.modal.querySelectorAll('.filter-date').forEach(input => input.value = '');
        
        this.applyFilters();
        this.renderTable();
    }

    /**
     * 全选/取消全选
     */
    toggleSelectAll(checked) {
        const checkboxes = this.modal.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const rowId = checkbox.value;
            if (checked) {
                this.selectedRows.add(rowId);
            } else {
                this.selectedRows.delete(rowId);
            }
        });
        
        this.updateBatchActions();
    }

    /**
     * 更新批量操作按钮状态
     */
    updateBatchActions() {
        const hasSelection = this.selectedRows.size > 0;
        
        this.modal.querySelector('[data-action="delete-selected"]').disabled = !hasSelection;
        this.modal.querySelector('[data-action="export-selected"]').disabled = !hasSelection;
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        if (!this.modal) return;

        const groupedTables = this.modal.querySelector('.grouped-tables');
        const statusText = this.modal.querySelector('.status-text');

        if (groupedTables) {
            groupedTables.style.opacity = show ? '0.5' : '1';
        }

        if (statusText) {
            statusText.textContent = show ? '正在加载数据...' : '数据加载完成';
        }

        console.log('[DataTable] 加载状态:', show ? '显示' : '隐藏');
    }

    /**
     * 显示/隐藏空状态
     */
    showEmpty(show) {
        if (!this.modal) return;

        const groupedTables = this.modal.querySelector('.grouped-tables');
        const statusText = this.modal.querySelector('.status-text');

        if (groupedTables) {
            if (show) {
                groupedTables.innerHTML = '<div class="empty-state">暂无数据</div>';
            }
        }

        if (statusText) {
            statusText.textContent = show ? '暂无数据' : '数据加载完成';
        }

        console.log('[DataTable] 空状态:', show ? '显示' : '隐藏');
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 显示数据表格界面
     */
    async show() {
        try {
            if (!this.initialized) {
                await this.init();
            }
            
            // 刷新数据
            await this.loadData();
            
            // 显示模态框
            this.modal.style.display = 'flex';
            this.visible = true;
            
            // 触发显示事件
            if (this.eventSystem) {
                this.eventSystem.emit('ui:show', {
                    component: 'DataTable',
                    timestamp: Date.now()
                });
            }
            
            console.log('[DataTable] 👁️ 数据表格界面已显示');
            
        } catch (error) {
            console.error('[DataTable] ❌ 显示界面失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 隐藏数据表格界面
     */
    hide() {
        try {
            this.modal.style.display = 'none';
            this.visible = false;
            
            // 触发隐藏事件
            if (this.eventSystem) {
                this.eventSystem.emit('ui:hide', {
                    component: 'DataTable',
                    timestamp: Date.now()
                });
            }
            
            console.log('[DataTable] 👁️ 数据表格界面已隐藏');
            
        } catch (error) {
            console.error('[DataTable] ❌ 隐藏界面失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 导出数据
     */
    async exportData() {
        try {
            const dataToExport = this.selectedRows.size > 0 
                ? this.filteredData.filter(item => this.selectedRows.has(String(item.id)))
                : this.filteredData;
            
            const exportData = {
                timestamp: Date.now(),
                version: '1.0.0',
                totalItems: dataToExport.length,
                data: dataToExport
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `data-export-${Date.now()}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            
            console.log(`[DataTable] 📤 导出了 ${dataToExport.length} 条数据`);
            
        } catch (error) {
            console.error('[DataTable] ❌ 导出数据失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 错误处理
     */
    handleError(error) {
        this.errorCount++;
        console.error(`[DataTable] ❌ 错误 #${this.errorCount}:`, error);
    }

    /**
     * 获取状态信息
     */
    getStatus() {
        return {
            initialized: this.initialized,
            visible: this.visible,
            dataCount: this.data.length,
            filteredCount: this.filteredData.length,
            selectedCount: this.selectedRows.size,
            currentPage: this.pagination.currentPage,
            totalPages: this.pagination.totalPages,
            errorCount: this.errorCount
        };
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.modal) {
            this.modal.remove();
            this.modal = null;
        }
        
        this.initialized = false;
        console.log('[DataTable] 💥 数据表格界面已销毁');
    }
}
