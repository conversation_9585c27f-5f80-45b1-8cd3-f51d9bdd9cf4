/**
 * 统一数据核心模块
 * 
 * 负责管理所有数据存储和同步，包括：
 * - localStorage（全局持久化数据）
 * - chatMetadata（角色/聊天相关数据）
 * - 数据同步和备份机制
 * - 数据验证和完整性检查
 * 
 * @class UnifiedDataCore
 */

export class UnifiedDataCore {
    constructor(eventSystem = null) {
        console.log('[UnifiedDataCore] 🔧 统一数据核心初始化开始');
        
        this.eventSystem = eventSystem;
        this.MODULE_NAME = 'information_bar_integration_tool';
        
        // SillyTavern上下文
        this.context = null;
        
        // 数据管理器
        this.localStorage = null;
        this.chatMetadata = null;
        
        // 数据缓存
        this.cache = new Map();
        
        // 同步状态
        this.syncInProgress = false;
        this.lastSyncTime = 0;
        this.syncInterval = 30000; // 30秒
        
        // 备份管理
        this.backupInterval = 300000; // 5分钟
        this.maxBackups = 10;
        
        // 初始化状态
        this.initialized = false;
        this.errorCount = 0;
        
        // 默认配置
        this.defaultSettings = Object.freeze({
            // 基础设置
            enabled: true,
            renderInChat: true,
            enableTableRecord: true,
            enableMemoryAssist: true,
            defaultCollapsed: false,
            
            // API配置
            apiConfig: {
                enabled: false,
                provider: 'gemini',
                format: 'native',
                endpoint: '',
                apiKey: '',
                model: '',
                temperature: 0.7,
                maxTokens: 2000,
                retryCount: 3,
                extraPrompt: ''
            },
            
            // 界面配置
            theme: {
                current: 'default',
                custom: {}
            },
            
            // 面板配置
            panels: {},
            
            // 数据管理配置
            dataManagement: {
                autoBackup: true,
                syncInterval: 30000,
                maxBackups: 10
            }
        });
        
        // 绑定方法
        this.init = this.init.bind(this);
        this.startAutoSync = this.startAutoSync.bind(this);
        this.syncData = this.syncData.bind(this);
        this.createBackup = this.createBackup.bind(this);
    }

    /**
     * 初始化数据核心
     */
    async init() {
        try {
            console.log('[UnifiedDataCore] 📊 开始初始化数据核心...');
            
            // 获取SillyTavern上下文
            this.context = SillyTavern.getContext();
            
            if (!this.context) {
                throw new Error('无法获取SillyTavern上下文');
            }
            
            // 初始化存储管理器
            this.initStorageManagers();
            
            // 初始化默认设置
            await this.initDefaultSettings();
            
            // 启动自动同步
            this.startAutoSync();
            
            this.initialized = true;
            console.log('[UnifiedDataCore] ✅ 数据核心初始化完成');
            
            // 触发初始化完成事件
            if (this.eventSystem) {
                this.eventSystem.emit('dataCore:initialized');
            }
            
        } catch (error) {
            console.error('[UnifiedDataCore] ❌ 初始化失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 初始化存储管理器
     */
    initStorageManagers() {
        const { extensionSettings, chatMetadata } = this.context;
        
        // localStorage管理器
        this.localStorage = {
            get: (key) => extensionSettings[this.MODULE_NAME]?.[key],
            set: (key, value) => {
                if (!extensionSettings[this.MODULE_NAME]) {
                    extensionSettings[this.MODULE_NAME] = {};
                }
                extensionSettings[this.MODULE_NAME][key] = value;
                this.context.saveSettingsDebounced();
            },
            delete: (key) => {
                if (extensionSettings[this.MODULE_NAME]) {
                    delete extensionSettings[this.MODULE_NAME][key];
                    this.context.saveSettingsDebounced();
                }
            },
            getAll: () => extensionSettings[this.MODULE_NAME] || {}
        };
        
        // chatMetadata管理器
        this.chatMetadata = {
            get: (key) => {
                const metadata = this.context.chatMetadata;
                return metadata?.[this.MODULE_NAME]?.[key];
            },
            set: async (key, value) => {
                const metadata = this.context.chatMetadata;
                if (!metadata[this.MODULE_NAME]) {
                    metadata[this.MODULE_NAME] = {};
                }
                metadata[this.MODULE_NAME][key] = value;
                await this.context.saveMetadata();
            },
            delete: async (key) => {
                const metadata = this.context.chatMetadata;
                if (metadata[this.MODULE_NAME]) {
                    delete metadata[this.MODULE_NAME][key];
                    await this.context.saveMetadata();
                }
            },
            getAll: () => {
                const metadata = this.context.chatMetadata;
                return metadata?.[this.MODULE_NAME] || {};
            }
        };
        
        console.log('[UnifiedDataCore] 🗄️ 存储管理器初始化完成');
    }

    /**
     * 初始化默认设置
     */
    async initDefaultSettings() {
        console.log('[UnifiedDataCore] ⚙️ 初始化默认设置...');
        
        const currentSettings = this.localStorage.getAll();
        
        // 合并默认设置
        const mergedSettings = this.mergeSettings(this.defaultSettings, currentSettings);
        
        // 保存合并后的设置
        for (const [key, value] of Object.entries(mergedSettings)) {
            this.localStorage.set(key, value);
        }
        
        console.log('[UnifiedDataCore] ✅ 默认设置初始化完成');
    }

    /**
     * 合并设置对象
     */
    mergeSettings(defaultSettings, currentSettings) {
        const merged = { ...defaultSettings };
        
        for (const [key, value] of Object.entries(currentSettings)) {
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                merged[key] = { ...merged[key], ...value };
            } else {
                merged[key] = value;
            }
        }
        
        return merged;
    }

    /**
     * 获取数据
     * @param {string} key - 数据键
     * @param {string} scope - 数据范围 ('global' | 'chat')
     * @returns {any} 数据值
     */
    async getData(key, scope = 'global') {
        try {
            // 先检查缓存
            const cacheKey = `${scope}:${key}`;
            if (this.cache.has(cacheKey)) {
                return this.cache.get(cacheKey);
            }
            
            let value;
            
            if (scope === 'global') {
                value = this.localStorage.get(key);
            } else if (scope === 'chat') {
                value = this.chatMetadata.get(key);
            } else {
                throw new Error(`无效的数据范围: ${scope}`);
            }
            
            // 更新缓存
            if (value !== undefined) {
                this.cache.set(cacheKey, value);
            }
            
            return value;
            
        } catch (error) {
            console.error('[UnifiedDataCore] ❌ 获取数据失败:', error);
            this.handleError(error);
            return undefined;
        }
    }

    /**
     * 设置数据
     * @param {string} key - 数据键
     * @param {any} value - 数据值
     * @param {string} scope - 数据范围 ('global' | 'chat')
     */
    async setData(key, value, scope = 'global') {
        try {
            if (scope === 'global') {
                this.localStorage.set(key, value);
            } else if (scope === 'chat') {
                await this.chatMetadata.set(key, value);
            } else {
                throw new Error(`无效的数据范围: ${scope}`);
            }
            
            // 更新缓存
            const cacheKey = `${scope}:${key}`;
            this.cache.set(cacheKey, value);
            
            // 触发数据变更事件
            if (this.eventSystem) {
                this.eventSystem.emit('data:changed', {
                    key,
                    value,
                    scope,
                    timestamp: Date.now()
                });
            }
            
        } catch (error) {
            console.error('[UnifiedDataCore] ❌ 设置数据失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 删除数据
     * @param {string} key - 数据键
     * @param {string} scope - 数据范围 ('global' | 'chat')
     */
    async deleteData(key, scope = 'global') {
        try {
            if (scope === 'global') {
                this.localStorage.delete(key);
            } else if (scope === 'chat') {
                await this.chatMetadata.delete(key);
            } else {
                throw new Error(`无效的数据范围: ${scope}`);
            }
            
            // 清除缓存
            const cacheKey = `${scope}:${key}`;
            this.cache.delete(cacheKey);
            
            // 触发数据删除事件
            if (this.eventSystem) {
                this.eventSystem.emit('data:deleted', {
                    key,
                    scope,
                    timestamp: Date.now()
                });
            }
            
        } catch (error) {
            console.error('[UnifiedDataCore] ❌ 删除数据失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 获取所有数据
     * @param {string} scope - 数据范围 ('global' | 'chat' | 'all')
     * @returns {Object} 所有数据
     */
    async getAllData(scope = 'all') {
        try {
            if (scope === 'global') {
                return this.localStorage.getAll();
            } else if (scope === 'chat') {
                return this.chatMetadata.getAll();
            } else if (scope === 'all') {
                return {
                    global: this.localStorage.getAll(),
                    chat: this.chatMetadata.getAll()
                };
            } else {
                throw new Error(`无效的数据范围: ${scope}`);
            }
        } catch (error) {
            console.error('[UnifiedDataCore] ❌ 获取所有数据失败:', error);
            this.handleError(error);
            return {};
        }
    }

    /**
     * 启动自动同步
     */
    startAutoSync() {
        console.log('[UnifiedDataCore] 🔄 启动自动同步...');
        
        // 数据同步定时器
        setInterval(() => {
            if (!this.syncInProgress) {
                this.syncData();
            }
        }, this.syncInterval);
        
        // 备份定时器
        setInterval(() => {
            this.createBackup();
        }, this.backupInterval);
        
        console.log('[UnifiedDataCore] ✅ 自动同步已启动');
    }

    /**
     * 同步数据
     */
    async syncData() {
        if (this.syncInProgress) {
            return;
        }
        
        try {
            this.syncInProgress = true;
            console.log('[UnifiedDataCore] 🔄 开始数据同步...');
            
            // 清理过期缓存
            this.cleanCache();
            
            // 验证数据完整性
            await this.validateDataIntegrity();
            
            this.lastSyncTime = Date.now();
            
            // 触发同步完成事件
            if (this.eventSystem) {
                this.eventSystem.emit('data:synced', {
                    timestamp: this.lastSyncTime
                });
            }
            
            console.log('[UnifiedDataCore] ✅ 数据同步完成');
            
        } catch (error) {
            console.error('[UnifiedDataCore] ❌ 数据同步失败:', error);
            this.handleError(error);
        } finally {
            this.syncInProgress = false;
        }
    }

    /**
     * 清理缓存
     */
    cleanCache() {
        // 清理超过1小时的缓存
        const maxAge = 60 * 60 * 1000; // 1小时
        const now = Date.now();
        
        for (const [key, entry] of this.cache.entries()) {
            if (entry.timestamp && (now - entry.timestamp) > maxAge) {
                this.cache.delete(key);
            }
        }
    }

    /**
     * 验证数据完整性
     */
    async validateDataIntegrity() {
        try {
            const globalData = this.localStorage.getAll();
            const chatData = this.chatMetadata.getAll();
            
            // 验证必要字段
            if (!globalData.enabled !== undefined) {
                await this.setData('enabled', this.defaultSettings.enabled, 'global');
            }
            
            console.log('[UnifiedDataCore] ✅ 数据完整性验证通过');
            
        } catch (error) {
            console.error('[UnifiedDataCore] ❌ 数据完整性验证失败:', error);
            throw error;
        }
    }

    /**
     * 创建备份
     */
    async createBackup() {
        try {
            console.log('[UnifiedDataCore] 💾 创建数据备份...');
            
            const backup = {
                timestamp: Date.now(),
                version: '1.0.0',
                data: await this.getAllData('all')
            };
            
            const backupKey = `backup_${backup.timestamp}`;
            await this.setData(backupKey, backup, 'global');
            
            // 清理旧备份
            await this.cleanOldBackups();
            
            console.log('[UnifiedDataCore] ✅ 数据备份完成');
            
        } catch (error) {
            console.error('[UnifiedDataCore] ❌ 创建备份失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 清理旧备份
     */
    async cleanOldBackups() {
        try {
            const allData = this.localStorage.getAll();
            const backupKeys = Object.keys(allData).filter(key => key.startsWith('backup_'));
            
            if (backupKeys.length > this.maxBackups) {
                // 按时间戳排序，删除最旧的备份
                backupKeys.sort((a, b) => {
                    const timestampA = parseInt(a.split('_')[1]);
                    const timestampB = parseInt(b.split('_')[1]);
                    return timestampA - timestampB;
                });
                
                const toDelete = backupKeys.slice(0, backupKeys.length - this.maxBackups);
                for (const key of toDelete) {
                    await this.deleteData(key, 'global');
                }
                
                console.log(`[UnifiedDataCore] 🗑️ 清理了 ${toDelete.length} 个旧备份`);
            }
        } catch (error) {
            console.error('[UnifiedDataCore] ❌ 清理旧备份失败:', error);
        }
    }

    /**
     * 导出所有数据
     */
    async exportAll() {
        return await this.getAllData('all');
    }

    /**
     * 导入数据
     */
    async importData(data) {
        try {
            console.log('[UnifiedDataCore] 📥 开始导入数据...');
            
            if (data.global) {
                for (const [key, value] of Object.entries(data.global)) {
                    await this.setData(key, value, 'global');
                }
            }
            
            if (data.chat) {
                for (const [key, value] of Object.entries(data.chat)) {
                    await this.setData(key, value, 'chat');
                }
            }
            
            console.log('[UnifiedDataCore] ✅ 数据导入完成');
            
        } catch (error) {
            console.error('[UnifiedDataCore] ❌ 数据导入失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 错误处理
     */
    handleError(error) {
        this.errorCount++;
        console.error(`[UnifiedDataCore] ❌ 错误 #${this.errorCount}:`, error);
        
        if (this.eventSystem) {
            this.eventSystem.emit('dataCore:error', {
                error: error.message,
                count: this.errorCount,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 获取状态信息
     */
    getStatus() {
        return {
            initialized: this.initialized,
            errorCount: this.errorCount,
            cacheSize: this.cache.size,
            lastSyncTime: this.lastSyncTime,
            syncInProgress: this.syncInProgress
        };
    }
}
