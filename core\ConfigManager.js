/**
 * 配置管理器
 * 
 * 负责管理扩展的所有配置：
 * - 基础设置管理
 * - API配置管理
 * - 面板配置管理
 * - 配置验证和默认值处理
 * - 配置变更通知
 * 
 * @class ConfigManager
 */

export class ConfigManager {
    constructor(dataCore) {
        console.log('[ConfigManager] 🔧 配置管理器初始化开始');
        
        this.dataCore = dataCore;
        
        // 配置缓存
        this.configCache = new Map();
        
        // 配置验证规则
        this.validationRules = {
            // 基础设置验证
            enabled: { type: 'boolean', default: true },
            renderInChat: { type: 'boolean', default: true },
            enableTableRecord: { type: 'boolean', default: true },
            enableMemoryAssist: { type: 'boolean', default: true },
            defaultCollapsed: { type: 'boolean', default: false },
            
            // API配置验证
            'apiConfig.enabled': { type: 'boolean', default: false },
            'apiConfig.provider': { type: 'string', enum: ['gemini', 'openai'], default: 'gemini' },
            'apiConfig.format': { type: 'string', enum: ['native', 'compatible'], default: 'native' },
            'apiConfig.endpoint': { type: 'string', default: '' },
            'apiConfig.apiKey': { type: 'string', default: '' },
            'apiConfig.model': { type: 'string', default: '' },
            'apiConfig.temperature': { type: 'number', min: 0, max: 2, default: 0.7 },
            'apiConfig.maxTokens': { type: 'number', min: 1, max: 100000, default: 2000 },
            'apiConfig.retryCount': { type: 'number', min: 0, max: 10, default: 3 },
            'apiConfig.extraPrompt': { type: 'string', default: '' },
            
            // 主题配置验证
            'theme.current': { type: 'string', default: 'default' },
            'theme.custom': { type: 'object', default: {} }
        };
        
        // 初始化状态
        this.initialized = false;
        this.errorCount = 0;
        
        // 绑定方法
        this.init = this.init.bind(this);
        this.getConfig = this.getConfig.bind(this);
        this.setConfig = this.setConfig.bind(this);
    }

    /**
     * 初始化配置管理器
     */
    async init() {
        try {
            console.log('[ConfigManager] 📊 开始初始化配置管理器...');
            
            if (!this.dataCore) {
                throw new Error('数据核心未初始化');
            }
            
            // 加载所有配置到缓存
            await this.loadAllConfigs();
            
            // 验证配置完整性
            await this.validateAllConfigs();
            
            this.initialized = true;
            console.log('[ConfigManager] ✅ 配置管理器初始化完成');
            
        } catch (error) {
            console.error('[ConfigManager] ❌ 初始化失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 加载所有配置到缓存
     */
    async loadAllConfigs() {
        try {
            const globalData = await this.dataCore.getAllData('global');
            
            // 将配置加载到缓存
            for (const [key, value] of Object.entries(globalData)) {
                this.configCache.set(key, value);
            }
            
            console.log(`[ConfigManager] 📥 加载了 ${this.configCache.size} 个配置项`);
            
        } catch (error) {
            console.error('[ConfigManager] ❌ 加载配置失败:', error);
            throw error;
        }
    }

    /**
     * 验证所有配置
     */
    async validateAllConfigs() {
        try {
            let fixedCount = 0;
            
            for (const [key, rule] of Object.entries(this.validationRules)) {
                const currentValue = await this.getConfig(key);
                const validatedValue = this.validateSingleConfig(key, currentValue, rule);
                
                if (validatedValue !== currentValue) {
                    await this.setConfig(key, validatedValue);
                    fixedCount++;
                }
            }
            
            if (fixedCount > 0) {
                console.log(`[ConfigManager] 🔧 修复了 ${fixedCount} 个配置项`);
            }
            
        } catch (error) {
            console.error('[ConfigManager] ❌ 验证配置失败:', error);
            throw error;
        }
    }

    /**
     * 验证单个配置项
     */
    validateSingleConfig(key, value, rule) {
        try {
            // 如果值为undefined或null，使用默认值
            if (value === undefined || value === null) {
                return rule.default;
            }
            
            // 类型验证
            if (rule.type && typeof value !== rule.type) {
                console.warn(`[ConfigManager] ⚠️ 配置项 ${key} 类型错误，使用默认值`);
                return rule.default;
            }
            
            // 枚举验证
            if (rule.enum && !rule.enum.includes(value)) {
                console.warn(`[ConfigManager] ⚠️ 配置项 ${key} 值不在允许范围内，使用默认值`);
                return rule.default;
            }
            
            // 数值范围验证
            if (rule.type === 'number') {
                if (rule.min !== undefined && value < rule.min) {
                    console.warn(`[ConfigManager] ⚠️ 配置项 ${key} 值过小，使用最小值`);
                    return rule.min;
                }
                if (rule.max !== undefined && value > rule.max) {
                    console.warn(`[ConfigManager] ⚠️ 配置项 ${key} 值过大，使用最大值`);
                    return rule.max;
                }
            }
            
            return value;
            
        } catch (error) {
            console.error(`[ConfigManager] ❌ 验证配置项失败 (${key}):`, error);
            return rule.default;
        }
    }

    /**
     * 获取配置值
     * @param {string} key - 配置键，支持点号分隔的嵌套键
     * @returns {any} 配置值
     */
    async getConfig(key) {
        try {
            // 先检查缓存
            if (this.configCache.has(key)) {
                return this.configCache.get(key);
            }
            
            // 处理嵌套键
            if (key.includes('.')) {
                return await this.getNestedConfig(key);
            }
            
            // 从数据核心获取
            const value = await this.dataCore.getData(key, 'global');
            
            // 更新缓存
            if (value !== undefined) {
                this.configCache.set(key, value);
            }
            
            return value;
            
        } catch (error) {
            console.error(`[ConfigManager] ❌ 获取配置失败 (${key}):`, error);
            this.handleError(error);
            
            // 返回默认值
            const rule = this.validationRules[key];
            return rule ? rule.default : undefined;
        }
    }

    /**
     * 获取嵌套配置
     */
    async getNestedConfig(key) {
        const keys = key.split('.');
        const rootKey = keys[0];
        
        // 获取根对象
        let rootValue = this.configCache.get(rootKey);
        if (!rootValue) {
            rootValue = await this.dataCore.getData(rootKey, 'global');
            if (rootValue) {
                this.configCache.set(rootKey, rootValue);
            }
        }
        
        if (!rootValue || typeof rootValue !== 'object') {
            return undefined;
        }
        
        // 遍历嵌套路径
        let current = rootValue;
        for (let i = 1; i < keys.length; i++) {
            if (current && typeof current === 'object' && keys[i] in current) {
                current = current[keys[i]];
            } else {
                return undefined;
            }
        }
        
        return current;
    }

    /**
     * 设置配置值
     * @param {string} key - 配置键
     * @param {any} value - 配置值
     * @param {boolean} validate - 是否验证配置
     */
    async setConfig(key, value, validate = true) {
        try {
            // 验证配置
            if (validate && this.validationRules[key]) {
                value = this.validateSingleConfig(key, value, this.validationRules[key]);
            }
            
            // 处理嵌套键
            if (key.includes('.')) {
                await this.setNestedConfig(key, value);
            } else {
                // 直接设置
                await this.dataCore.setData(key, value, 'global');
                this.configCache.set(key, value);
            }
            
            // 触发配置变更事件
            if (this.dataCore.eventSystem) {
                this.dataCore.eventSystem.emit('config:changed', {
                    key,
                    value,
                    timestamp: Date.now()
                });
            }
            
            console.log(`[ConfigManager] ✅ 配置已更新: ${key}`);
            
        } catch (error) {
            console.error(`[ConfigManager] ❌ 设置配置失败 (${key}):`, error);
            this.handleError(error);
        }
    }

    /**
     * 设置嵌套配置
     */
    async setNestedConfig(key, value) {
        const keys = key.split('.');
        const rootKey = keys[0];
        
        // 获取根对象
        let rootValue = this.configCache.get(rootKey);
        if (!rootValue) {
            rootValue = await this.dataCore.getData(rootKey, 'global');
        }
        
        // 如果根对象不存在，创建一个
        if (!rootValue || typeof rootValue !== 'object') {
            rootValue = {};
        }
        
        // 深拷贝根对象以避免引用问题
        rootValue = JSON.parse(JSON.stringify(rootValue));
        
        // 遍历并设置嵌套值
        let current = rootValue;
        for (let i = 1; i < keys.length - 1; i++) {
            if (!current[keys[i]] || typeof current[keys[i]] !== 'object') {
                current[keys[i]] = {};
            }
            current = current[keys[i]];
        }
        
        // 设置最终值
        current[keys[keys.length - 1]] = value;
        
        // 保存根对象
        await this.dataCore.setData(rootKey, rootValue, 'global');
        this.configCache.set(rootKey, rootValue);
    }

    /**
     * 删除配置
     * @param {string} key - 配置键
     */
    async deleteConfig(key) {
        try {
            await this.dataCore.deleteData(key, 'global');
            this.configCache.delete(key);
            
            // 触发配置删除事件
            if (this.dataCore.eventSystem) {
                this.dataCore.eventSystem.emit('config:deleted', {
                    key,
                    timestamp: Date.now()
                });
            }
            
            console.log(`[ConfigManager] 🗑️ 配置已删除: ${key}`);
            
        } catch (error) {
            console.error(`[ConfigManager] ❌ 删除配置失败 (${key}):`, error);
            this.handleError(error);
        }
    }

    /**
     * 重置配置到默认值
     * @param {string} key - 配置键，如果为空则重置所有配置
     */
    async resetConfig(key = null) {
        try {
            if (key) {
                // 重置单个配置
                const rule = this.validationRules[key];
                if (rule) {
                    await this.setConfig(key, rule.default, false);
                    console.log(`[ConfigManager] 🔄 配置已重置: ${key}`);
                }
            } else {
                // 重置所有配置
                let resetCount = 0;
                for (const [configKey, rule] of Object.entries(this.validationRules)) {
                    await this.setConfig(configKey, rule.default, false);
                    resetCount++;
                }
                console.log(`[ConfigManager] 🔄 已重置 ${resetCount} 个配置项`);
            }
            
            // 触发配置重置事件
            if (this.dataCore.eventSystem) {
                this.dataCore.eventSystem.emit('config:reset', {
                    key,
                    timestamp: Date.now()
                });
            }
            
        } catch (error) {
            console.error('[ConfigManager] ❌ 重置配置失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 获取所有配置
     */
    async getAllConfigs() {
        try {
            const configs = {};
            
            for (const key of this.configCache.keys()) {
                configs[key] = this.configCache.get(key);
            }
            
            return configs;
            
        } catch (error) {
            console.error('[ConfigManager] ❌ 获取所有配置失败:', error);
            this.handleError(error);
            return {};
        }
    }

    /**
     * 批量设置配置
     * @param {Object} configs - 配置对象
     */
    async setConfigs(configs) {
        try {
            let updateCount = 0;
            
            for (const [key, value] of Object.entries(configs)) {
                await this.setConfig(key, value);
                updateCount++;
            }
            
            console.log(`[ConfigManager] ✅ 批量更新了 ${updateCount} 个配置项`);
            
        } catch (error) {
            console.error('[ConfigManager] ❌ 批量设置配置失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 导出配置
     */
    async exportConfigs() {
        try {
            const configs = await this.getAllConfigs();
            
            return {
                timestamp: Date.now(),
                version: '1.0.0',
                configs
            };
            
        } catch (error) {
            console.error('[ConfigManager] ❌ 导出配置失败:', error);
            this.handleError(error);
            return null;
        }
    }

    /**
     * 导入配置
     * @param {Object} exportData - 导出的配置数据
     */
    async importConfigs(exportData) {
        try {
            if (!exportData || !exportData.configs) {
                throw new Error('无效的配置数据');
            }
            
            await this.setConfigs(exportData.configs);
            
            console.log('[ConfigManager] 📥 配置导入完成');
            
        } catch (error) {
            console.error('[ConfigManager] ❌ 导入配置失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 错误处理
     */
    handleError(error) {
        this.errorCount++;
        console.error(`[ConfigManager] ❌ 错误 #${this.errorCount}:`, error);
    }

    /**
     * 获取状态信息
     */
    getStatus() {
        return {
            initialized: this.initialized,
            errorCount: this.errorCount,
            cacheSize: this.configCache.size,
            validationRulesCount: Object.keys(this.validationRules).length
        };
    }
}
