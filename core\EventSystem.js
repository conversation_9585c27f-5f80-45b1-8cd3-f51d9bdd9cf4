/**
 * 事件管理系统
 * 
 * 负责管理扩展内部事件和SillyTavern事件集成：
 * - 内部事件的注册、触发和监听
 * - SillyTavern事件的代理和转发
 * - 事件队列和异步处理
 * - 事件日志和调试支持
 * 
 * @class EventSystem
 */

export class EventSystem {
    constructor() {
        console.log('[EventSystem] 🔧 事件管理系统初始化开始');
        
        // 事件监听器映射
        this.listeners = new Map();
        
        // SillyTavern事件系统引用
        this.sillyTavernEventSource = null;
        this.sillyTavernEventTypes = null;
        
        // 事件队列
        this.eventQueue = [];
        this.processingQueue = false;
        
        // 事件统计
        this.eventStats = {
            emitted: 0,
            processed: 0,
            errors: 0
        };
        
        // 调试模式
        this.debugMode = false;
        
        // 初始化状态
        this.initialized = false;
        this.errorCount = 0;
        
        // 预定义事件类型
        this.EVENT_TYPES = Object.freeze({
            // 系统事件
            SYSTEM_READY: 'system:ready',
            SYSTEM_ERROR: 'system:error',
            
            // 数据事件
            DATA_CHANGED: 'data:changed',
            DATA_DELETED: 'data:deleted',
            DATA_SYNCED: 'data:synced',
            DATA_BACKUP_CREATED: 'data:backup:created',
            
            // UI事件
            UI_SHOW: 'ui:show',
            UI_HIDE: 'ui:hide',
            UI_TOGGLE: 'ui:toggle',
            UI_REFRESH: 'ui:refresh',
            
            // API事件
            API_REQUEST: 'api:request',
            API_RESPONSE: 'api:response',
            API_ERROR: 'api:error',
            
            // 聊天事件
            CHAT_CHANGED: 'chat:changed',
            MESSAGE_RECEIVED: 'message:received',
            MESSAGE_SENT: 'message:sent',
            
            // 配置事件
            CONFIG_CHANGED: 'config:changed',
            CONFIG_RESET: 'config:reset',
            
            // 面板事件
            PANEL_CREATED: 'panel:created',
            PANEL_UPDATED: 'panel:updated',
            PANEL_DELETED: 'panel:deleted'
        });
        
        // 绑定方法
        this.init = this.init.bind(this);
        this.on = this.on.bind(this);
        this.off = this.off.bind(this);
        this.emit = this.emit.bind(this);
        this.processEventQueue = this.processEventQueue.bind(this);
        
        // 自动初始化
        this.init();
    }

    /**
     * 初始化事件系统
     */
    init() {
        try {
            console.log('[EventSystem] 📊 开始初始化事件系统...');
            
            // 获取SillyTavern事件系统
            this.bindSillyTavernEvents();
            
            // 启动事件队列处理
            this.startEventQueueProcessor();
            
            this.initialized = true;
            console.log('[EventSystem] ✅ 事件系统初始化完成');
            
            // 触发系统就绪事件
            this.emit(this.EVENT_TYPES.SYSTEM_READY, {
                timestamp: Date.now(),
                version: '1.0.0'
            });
            
        } catch (error) {
            console.error('[EventSystem] ❌ 初始化失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 绑定SillyTavern事件系统
     */
    bindSillyTavernEvents() {
        try {
            // 获取SillyTavern上下文
            const context = SillyTavern?.getContext?.();
            
            if (context) {
                this.sillyTavernEventSource = context.eventSource;
                this.sillyTavernEventTypes = context.event_types;
                
                console.log('[EventSystem] 🔗 SillyTavern事件系统绑定成功');
            } else {
                console.warn('[EventSystem] ⚠️ 无法获取SillyTavern事件系统，将在稍后重试');
                
                // 延迟重试
                setTimeout(() => {
                    this.bindSillyTavernEvents();
                }, 1000);
            }
        } catch (error) {
            console.error('[EventSystem] ❌ 绑定SillyTavern事件系统失败:', error);
        }
    }

    /**
     * 启动事件队列处理器
     */
    startEventQueueProcessor() {
        setInterval(() => {
            if (!this.processingQueue && this.eventQueue.length > 0) {
                this.processEventQueue();
            }
        }, 10); // 10ms间隔处理事件队列
    }

    /**
     * 处理事件队列
     */
    async processEventQueue() {
        if (this.processingQueue || this.eventQueue.length === 0) {
            return;
        }
        
        this.processingQueue = true;
        
        try {
            while (this.eventQueue.length > 0) {
                const event = this.eventQueue.shift();
                await this.processEvent(event);
                this.eventStats.processed++;
            }
        } catch (error) {
            console.error('[EventSystem] ❌ 处理事件队列失败:', error);
            this.handleError(error);
        } finally {
            this.processingQueue = false;
        }
    }

    /**
     * 处理单个事件
     */
    async processEvent(event) {
        try {
            const { type, data, timestamp } = event;
            
            if (this.debugMode) {
                console.log(`[EventSystem] 🎯 处理事件: ${type}`, data);
            }
            
            // 获取事件监听器
            const listeners = this.listeners.get(type) || [];
            
            // 并行执行所有监听器
            const promises = listeners.map(async (listener) => {
                try {
                    if (typeof listener === 'function') {
                        await listener(data, { type, timestamp });
                    }
                } catch (error) {
                    console.error(`[EventSystem] ❌ 监听器执行失败 (${type}):`, error);
                    this.eventStats.errors++;
                }
            });
            
            await Promise.all(promises);
            
        } catch (error) {
            console.error('[EventSystem] ❌ 处理事件失败:', error);
            this.eventStats.errors++;
        }
    }

    /**
     * 注册事件监听器
     * @param {string} eventType - 事件类型
     * @param {Function} callback - 回调函数
     * @returns {Function} 取消监听的函数
     */
    on(eventType, callback) {
        try {
            if (typeof eventType !== 'string') {
                throw new Error('事件类型必须是字符串');
            }
            
            if (typeof callback !== 'function') {
                throw new Error('回调函数必须是函数');
            }
            
            // 获取或创建监听器数组
            if (!this.listeners.has(eventType)) {
                this.listeners.set(eventType, []);
            }
            
            const listeners = this.listeners.get(eventType);
            listeners.push(callback);
            
            if (this.debugMode) {
                console.log(`[EventSystem] 📝 注册监听器: ${eventType} (总数: ${listeners.length})`);
            }
            
            // 返回取消监听的函数
            return () => {
                this.off(eventType, callback);
            };
            
        } catch (error) {
            console.error('[EventSystem] ❌ 注册监听器失败:', error);
            this.handleError(error);
            return () => {}; // 返回空函数避免错误
        }
    }

    /**
     * 取消事件监听器
     * @param {string} eventType - 事件类型
     * @param {Function} callback - 回调函数
     */
    off(eventType, callback) {
        try {
            const listeners = this.listeners.get(eventType);
            
            if (listeners) {
                const index = listeners.indexOf(callback);
                if (index > -1) {
                    listeners.splice(index, 1);
                    
                    if (this.debugMode) {
                        console.log(`[EventSystem] 🗑️ 取消监听器: ${eventType} (剩余: ${listeners.length})`);
                    }
                    
                    // 如果没有监听器了，删除整个条目
                    if (listeners.length === 0) {
                        this.listeners.delete(eventType);
                    }
                }
            }
        } catch (error) {
            console.error('[EventSystem] ❌ 取消监听器失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 触发事件
     * @param {string} eventType - 事件类型
     * @param {any} data - 事件数据
     * @param {Object} options - 选项
     */
    async emit(eventType, data = null, options = {}) {
        try {
            if (typeof eventType !== 'string') {
                throw new Error('事件类型必须是字符串');
            }
            
            const event = {
                type: eventType,
                data,
                timestamp: Date.now(),
                ...options
            };
            
            this.eventStats.emitted++;
            
            if (this.debugMode) {
                console.log(`[EventSystem] 🚀 触发事件: ${eventType}`, data);
            }
            
            // 如果是同步模式，直接处理
            if (options.sync) {
                await this.processEvent(event);
            } else {
                // 否则加入队列异步处理
                this.eventQueue.push(event);
            }
            
        } catch (error) {
            console.error('[EventSystem] ❌ 触发事件失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 一次性监听器
     * @param {string} eventType - 事件类型
     * @param {Function} callback - 回调函数
     * @returns {Function} 取消监听的函数
     */
    once(eventType, callback) {
        const wrappedCallback = (data, meta) => {
            callback(data, meta);
            this.off(eventType, wrappedCallback);
        };
        
        return this.on(eventType, wrappedCallback);
    }

    /**
     * 等待特定事件
     * @param {string} eventType - 事件类型
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise} Promise对象
     */
    waitFor(eventType, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                this.off(eventType, handler);
                reject(new Error(`等待事件超时: ${eventType}`));
            }, timeout);
            
            const handler = (data, meta) => {
                clearTimeout(timer);
                resolve({ data, meta });
            };
            
            this.once(eventType, handler);
        });
    }

    /**
     * 代理SillyTavern事件
     * @param {string} sillyTavernEventType - SillyTavern事件类型
     * @param {string} internalEventType - 内部事件类型
     */
    proxySillyTavernEvent(sillyTavernEventType, internalEventType) {
        if (!this.sillyTavernEventSource) {
            console.warn('[EventSystem] ⚠️ SillyTavern事件系统未就绪，无法代理事件');
            return;
        }
        
        try {
            this.sillyTavernEventSource.on(sillyTavernEventType, (data) => {
                this.emit(internalEventType, data);
            });
            
            console.log(`[EventSystem] 🔄 代理事件: ${sillyTavernEventType} -> ${internalEventType}`);
            
        } catch (error) {
            console.error('[EventSystem] ❌ 代理事件失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 批量注册事件代理
     */
    setupEventProxies() {
        if (!this.sillyTavernEventTypes) {
            console.warn('[EventSystem] ⚠️ SillyTavern事件类型未就绪');
            return;
        }
        
        const proxies = [
            [this.sillyTavernEventTypes.CHAT_CHANGED, this.EVENT_TYPES.CHAT_CHANGED],
            [this.sillyTavernEventTypes.MESSAGE_RECEIVED, this.EVENT_TYPES.MESSAGE_RECEIVED],
            [this.sillyTavernEventTypes.MESSAGE_SENT, this.EVENT_TYPES.MESSAGE_SENT]
        ];
        
        proxies.forEach(([stEvent, internalEvent]) => {
            this.proxySillyTavernEvent(stEvent, internalEvent);
        });
        
        console.log('[EventSystem] 🔗 事件代理设置完成');
    }

    /**
     * 获取事件统计信息
     */
    getStats() {
        return {
            ...this.eventStats,
            listenersCount: this.listeners.size,
            queueLength: this.eventQueue.length,
            processingQueue: this.processingQueue
        };
    }

    /**
     * 清理所有监听器
     */
    clearAllListeners() {
        this.listeners.clear();
        console.log('[EventSystem] 🧹 所有监听器已清理');
    }

    /**
     * 设置调试模式
     * @param {boolean} enabled - 是否启用调试模式
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`[EventSystem] 🐛 调试模式: ${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 错误处理
     */
    handleError(error) {
        this.errorCount++;
        console.error(`[EventSystem] ❌ 错误 #${this.errorCount}:`, error);
        
        // 触发系统错误事件
        if (this.initialized) {
            this.emit(this.EVENT_TYPES.SYSTEM_ERROR, {
                error: error.message,
                count: this.errorCount,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 获取状态信息
     */
    getStatus() {
        return {
            initialized: this.initialized,
            errorCount: this.errorCount,
            debugMode: this.debugMode,
            stats: this.getStats(),
            sillyTavernBound: !!this.sillyTavernEventSource
        };
    }

    /**
     * 销毁事件系统
     */
    destroy() {
        this.clearAllListeners();
        this.eventQueue.length = 0;
        this.initialized = false;
        console.log('[EventSystem] 💥 事件系统已销毁');
    }
}
