/**
 * 信息栏设置界面
 * 
 * 负责管理信息栏的设置界面：
 * - 基础设置面板
 * - API配置面板
 * - 主题设置面板
 * - 面板管理界面
 * - 设置导入导出功能
 * 
 * @class InfoBarSettings
 */

export class InfoBarSettings {
    constructor(configManager, apiIntegration, eventSystem) {
        console.log('[InfoBarSettings] 🔧 信息栏设置界面初始化开始');
        
        this.configManager = configManager;
        this.apiIntegration = apiIntegration;
        this.eventSystem = eventSystem;
        
        // UI元素引用
        this.container = null;
        this.modal = null;
        this.currentTab = 'basic';
        
        // 设置面板
        this.panels = {
            basic: null,
            api: null,
            theme: null,
            panels: null,
            advanced: null,
            personal: null,
            interaction: null,
            tasks: null,
            world: null,
            organization: null,
            news: null,
            inventory: null,
            abilities: null,
            plot: null,
            cultivation: null,
            fantasy: null,
            modern: null,
            historical: null,
            magic: null,
            training: null
        };
        
        // 表单数据
        this.formData = {};
        
        // 初始化状态
        this.initialized = false;
        this.visible = false;
        this.errorCount = 0;
        
        // 绑定方法
        this.init = this.init.bind(this);
        this.show = this.show.bind(this);
        this.hide = this.hide.bind(this);
        this.createUI = this.createUI.bind(this);
        this.loadSettings = this.loadSettings.bind(this);
        this.saveSettings = this.saveSettings.bind(this);
    }

    /**
     * 初始化设置界面
     */
    async init() {
        try {
            console.log('[InfoBarSettings] 📊 开始初始化设置界面...');
            
            if (!this.configManager) {
                throw new Error('配置管理器未初始化');
            }
            
            // 创建UI
            this.createUI();
            
            // 加载当前设置
            await this.loadSettings();
            
            // 绑定事件
            this.bindEvents();
            
            this.initialized = true;
            console.log('[InfoBarSettings] ✅ 设置界面初始化完成');
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 初始化失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 创建UI界面
     */
    createUI() {
        try {
            // 创建模态框容器
            this.modal = document.createElement('div');
            this.modal.id = 'info-bar-settings-modal';
            this.modal.className = 'info-bar-settings-modal infobar-modal-new';
            this.modal.style.display = 'none';
            
            this.modal.innerHTML = `
                <div class="modal-overlay" onclick="this.closest('.info-bar-settings-modal').style.display='none'"></div>
                <div class="modal-container">
                    <!-- 顶部标题栏 -->
                    <div class="modal-header">
                        <div class="header-left">
                            <div class="header-icon">⚙️</div>
                            <h2>信息栏设置</h2>
                        </div>
                        <div class="header-right">
                            <div class="success-notification" style="display: block;">
                                <span class="success-icon">✅</span>
                                <span class="success-text">信息栏系统已成功加载！</span>
                            </div>
                            <button class="modal-close" onclick="this.closest('.info-bar-settings-modal').style.display='none'">×</button>
                        </div>
                    </div>

                    <!-- 主体内容区域 -->
                    <div class="modal-body">
                        <!-- 左侧导航栏 -->
                        <div class="sidebar-nav">
                            <div class="nav-item active" data-nav="basic">
                                <span class="nav-icon">⚙️</span>
                                <span class="nav-text">基础设置</span>
                            </div>
                            <div class="nav-item" data-nav="api">
                                <span class="nav-icon">☁️</span>
                                <span class="nav-text">自定义API</span>
                            </div>
                            <div class="nav-item" data-nav="personal">
                                <span class="nav-icon">👤</span>
                                <span class="nav-text">个人信息</span>
                            </div>
                            <div class="nav-item" data-nav="interaction">
                                <span class="nav-icon">👥</span>
                                <span class="nav-text">交互对象</span>
                            </div>
                            <div class="nav-item" data-nav="tasks">
                                <span class="nav-icon">📋</span>
                                <span class="nav-text">任务系统</span>
                            </div>
                            <div class="nav-item" data-nav="world">
                                <span class="nav-icon">🌍</span>
                                <span class="nav-text">世界信息</span>
                            </div>
                            <div class="nav-item" data-nav="organization">
                                <span class="nav-icon">🏢</span>
                                <span class="nav-text">组织信息</span>
                            </div>
                            <div class="nav-item" data-nav="news">
                                <span class="nav-icon">📰</span>
                                <span class="nav-text">资讯内容</span>
                            </div>
                            <div class="nav-item" data-nav="inventory">
                                <span class="nav-icon">🎒</span>
                                <span class="nav-text">背包仓库</span>
                            </div>
                            <div class="nav-item" data-nav="abilities">
                                <span class="nav-icon">⚡</span>
                                <span class="nav-text">能力系统</span>
                            </div>
                            <div class="nav-item" data-nav="plot">
                                <span class="nav-icon">📖</span>
                                <span class="nav-text">剧情面板</span>
                            </div>
                            <div class="nav-item" data-nav="cultivation">
                                <span class="nav-icon">🧘</span>
                                <span class="nav-text">修仙世界</span>
                            </div>
                            <div class="nav-item" data-nav="fantasy">
                                <span class="nav-icon">🔮</span>
                                <span class="nav-text">玄幻世界</span>
                            </div>
                            <div class="nav-item" data-nav="modern">
                                <span class="nav-icon">🏙️</span>
                                <span class="nav-text">都市现代</span>
                            </div>
                            <div class="nav-item" data-nav="historical">
                                <span class="nav-icon">🏛️</span>
                                <span class="nav-text">历史古代</span>
                            </div>
                            <div class="nav-item" data-nav="magic">
                                <span class="nav-icon">🪄</span>
                                <span class="nav-text">魔法能力</span>
                            </div>
                            <div class="nav-item" data-nav="training">
                                <span class="nav-icon">🎯</span>
                                <span class="nav-text">调教系统</span>
                            </div>
                            <div class="nav-item" data-nav="theme">
                                <span class="nav-icon">🎨</span>
                                <span class="nav-text">主题设置</span>
                            </div>
                            <div class="nav-item" data-nav="advanced">
                                <span class="nav-icon">🔧</span>
                                <span class="nav-text">高级设置</span>
                            </div>

                            <!-- 底部操作按钮 -->
                            <div class="nav-bottom">
                                <button class="btn-reset">恢复所有设置</button>
                            </div>
                        </div>

                        <!-- 右侧内容区域 -->
                        <div class="content-area">
                            <div class="content-panel active" data-content="basic">
                                ${this.createBasicPanelNew()}
                            </div>
                            <div class="content-panel" data-content="api">
                                ${this.createAPIPanel()}
                            </div>
                            <div class="content-panel" data-content="personal">
                                ${this.createPersonalPanel()}
                            </div>
                            <div class="content-panel" data-content="interaction">
                                ${this.createInteractionPanel()}
                            </div>
                            <div class="content-panel" data-content="tasks">
                                ${this.createTasksPanel()}
                            </div>
                            <div class="content-panel" data-content="world">
                                ${this.createWorldPanel()}
                            </div>
                            <div class="content-panel" data-content="organization">
                                ${this.createOrganizationPanel()}
                            </div>
                            <div class="content-panel" data-content="news">
                                ${this.createNewsPanel()}
                            </div>
                            <div class="content-panel" data-content="inventory">
                                ${this.createInventoryPanel()}
                            </div>
                            <div class="content-panel" data-content="abilities">
                                ${this.createAbilitiesPanel()}
                            </div>
                            <div class="content-panel" data-content="plot">
                                ${this.createPlotPanel()}
                            </div>
                            <div class="content-panel" data-content="cultivation">
                                ${this.createCultivationPanel()}
                            </div>
                            <div class="content-panel" data-content="fantasy">
                                ${this.createFantasyPanel()}
                            </div>
                            <div class="content-panel" data-content="modern">
                                ${this.createModernPanel()}
                            </div>
                            <div class="content-panel" data-content="historical">
                                ${this.createHistoricalPanel()}
                            </div>
                            <div class="content-panel" data-content="magic">
                                ${this.createMagicPanel()}
                            </div>
                            <div class="content-panel" data-content="training">
                                ${this.createTrainingPanel()}
                            </div>
                            <div class="content-panel" data-content="theme">
                                ${this.createThemePanel()}
                            </div>
                            <div class="content-panel" data-content="advanced">
                                ${this.createAdvancedPanel()}
                            </div>
                        </div>
                    </div>

                    <!-- 底部操作栏 -->
                    <div class="modal-footer">
                        <div class="footer-left">
                            <span class="status-text">就绪</span>
                        </div>
                        <div class="footer-right">
                            <button class="btn-cancel" data-action="close">取消</button>
                            <button class="btn-save" data-action="save">保存设置</button>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加到页面
            document.body.appendChild(this.modal);

            // 绑定新的事件
            this.bindNewEvents();

            console.log('[InfoBarSettings] 🎨 新UI界面创建完成');

        } catch (error) {
            console.error('[InfoBarSettings] ❌ 创建UI失败:', error);
            throw error;
        }
    }

    /**
     * 创建新的基础设置面板 - 复选框形式
     */
    createBasicPanelNew() {
        return `
            <div class="content-header">
                <h3>基础功能配置</h3>
            </div>

            <div class="content-body">
                <!-- 基础功能卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">⚙️</div>
                        <div class="card-title">基础功能</div>
                        <div class="card-subtitle">信息栏的核心功能设置</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">8/15 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="auto-save-checkbox" name="basic.autoSave.enabled" checked />
                                <label for="auto-save-checkbox" class="checkbox-label">自动保存</label>
                            </div>
                            <div class="sub-item-desc">自动保存用户数据和设置</div>
                        </div>

                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="real-time-sync-checkbox" name="basic.realTimeSync.enabled" checked />
                                <label for="real-time-sync-checkbox" class="checkbox-label">实时同步</label>
                            </div>
                            <div class="sub-item-desc">实时同步数据到云端</div>
                        </div>

                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="notifications-checkbox" name="basic.notifications.enabled" />
                                <label for="notifications-checkbox" class="checkbox-label">通知提醒</label>
                            </div>
                            <div class="sub-item-desc">显示系统通知和提醒</div>
                        </div>

                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="dark-theme-checkbox" name="basic.darkTheme.enabled" checked />
                                <label for="dark-theme-checkbox" class="checkbox-label">深色主题</label>
                            </div>
                            <div class="sub-item-desc">使用深色界面主题</div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="auto-backup-checkbox" name="basic.autoBackup.enabled" checked />
                                <label for="auto-backup-checkbox" class="checkbox-label">自动备份</label>
                            </div>
                            <div class="sub-item-desc">定期自动备份数据</div>
                        </div>

                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="data-validation-checkbox" name="basic.dataValidation.enabled" checked />
                                <label for="data-validation-checkbox" class="checkbox-label">数据验证</label>
                            </div>
                            <div class="sub-item-desc">验证输入数据的有效性</div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="error-logging-checkbox" name="basic.errorLogging.enabled" />
                                <label for="error-logging-checkbox" class="checkbox-label">错误日志</label>
                            </div>
                            <div class="sub-item-desc">记录系统错误和异常</div>
                        </div>

                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="performance-monitor-checkbox" name="basic.performanceMonitor.enabled" />
                                <label for="performance-monitor-checkbox" class="checkbox-label">性能监控</label>
                            </div>
                            <div class="sub-item-desc">监控系统性能指标</div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cache-management-checkbox" name="basic.cacheManagement.enabled" checked />
                                <label for="cache-management-checkbox" class="checkbox-label">缓存管理</label>
                            </div>
                            <div class="sub-item-desc">智能管理数据缓存</div>
                        </div>

                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="security-check-checkbox" name="basic.securityCheck.enabled" checked />
                                <label for="security-check-checkbox" class="checkbox-label">安全检查</label>
                            </div>
                            <div class="sub-item-desc">定期进行安全检查</div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮区域 -->
                <div class="action-buttons">
                    <button class="btn-action btn-all">全选</button>
                    <button class="btn-action btn-none">全不选</button>
                    <button class="btn-action btn-recommended">推荐配置</button>
                </div>
            </div>
        `;
    }

    /**
     * 绑定新的事件处理
     */
    bindNewEvents() {
        try {
            // 导航切换事件
            this.modal.addEventListener('click', (e) => {
                if (e.target.closest('.nav-item')) {
                    const navItem = e.target.closest('.nav-item');
                    const navType = navItem.dataset.nav;
                    this.switchToContent(navType);
                }
            });

            // 按钮点击事件
            this.modal.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                if (action) {
                    this.handleAction(action);
                }
            });

            // 复选框变更事件
            this.modal.addEventListener('change', (e) => {
                if (e.target.type === 'checkbox') {
                    this.handleCheckboxChange(e);
                }
            });

            console.log('[InfoBarSettings] 🔗 新事件绑定完成');

        } catch (error) {
            console.error('[InfoBarSettings] ❌ 绑定新事件失败:', error);
            throw error;
        }
    }

    /**
     * 切换内容面板
     */
    switchToContent(contentType) {
        try {
            // 更新导航状态
            this.modal.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            this.modal.querySelector(`[data-nav="${contentType}"]`).classList.add('active');

            // 更新内容面板
            this.modal.querySelectorAll('.content-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            this.modal.querySelector(`[data-content="${contentType}"]`).classList.add('active');

            console.log(`[InfoBarSettings] 📑 切换到内容: ${contentType}`);

        } catch (error) {
            console.error('[InfoBarSettings] ❌ 切换内容失败:', error);
        }
    }

    /**
     * 处理操作按钮
     */
    handleAction(action) {
        try {
            switch (action) {
                case 'close':
                    this.hide();
                    break;
                case 'save':
                    this.saveSettings();
                    break;
                case 'reset':
                    this.resetSettings();
                    break;
                default:
                    console.log(`[InfoBarSettings] 🔘 处理操作: ${action}`);
            }
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 处理操作失败:', error);
        }
    }

    /**
     * 处理复选框变更
     */
    handleCheckboxChange(e) {
        try {
            const name = e.target.name;
            const checked = e.target.checked;

            console.log(`[InfoBarSettings] ☑️ 复选框变更: ${name} = ${checked}`);

            // 特殊处理API配置开关
            if (name === 'apiConfig.enabled') {
                const apiConfigContent = this.modal.querySelector('.api-config-content');
                if (apiConfigContent) {
                    apiConfigContent.style.display = checked ? 'block' : 'none';
                    console.log(`[InfoBarSettings] 🔌 API配置区域${checked ? '显示' : '隐藏'}`);
                }
            }

            // 如果是主开关，控制相关子项
            if (name && name.includes('.enabled')) {
                const baseName = name.replace('.enabled', '');
                const relatedInputs = this.modal.querySelectorAll(`[name^="${baseName}."]`);
                relatedInputs.forEach(input => {
                    if (input !== e.target) {
                        input.disabled = !checked;
                    }
                });
            }

        } catch (error) {
            console.error('[InfoBarSettings] ❌ 处理复选框变更失败:', error);
        }
    }



    /**
     * 创建API配置面板
     */
    createAPIPanel() {
        return `
            <div class="settings-group">
                <h3>🔌 API功能控制</h3>
                <div class="form-group">
                    <label class="switch-label">
                        <input type="checkbox" name="apiConfig.enabled" data-action="toggle-api-config" />
                        <span class="switch-slider"></span>
                        启用API功能
                    </label>
                    <small>开启后可以使用AI模型进行智能处理</small>
                </div>
            </div>

            <div class="api-config-content" style="display: none;">
                <div class="settings-group">
                    <h3>API提供商选择</h3>
                    <div class="form-group">
                        <label>选择API提供商</label>
                        <select name="apiConfig.provider" onchange="this.closest('.tab-panel').querySelector('.provider-config').style.display = 'none'; this.closest('.tab-panel').querySelector('.provider-' + this.value).style.display = 'block';">
                            <option value="">请选择提供商</option>
                            <option value="gemini">Google Gemini</option>
                            <option value="openai">OpenAI</option>
                        </select>
                    </div>
                </div>

            <!-- Gemini API 配置 -->
            <div class="provider-config provider-gemini" style="display: none;">
                <div class="settings-group">
                    <h3>🔷 Gemini API 配置</h3>
                    <div class="form-group">
                        <label>接口类型</label>
                        <select name="gemini.interfaceType" onchange="this.closest('.provider-gemini').querySelector('.gemini-native').style.display = this.value === 'native' ? 'block' : 'none'; this.closest('.provider-gemini').querySelector('.gemini-compatible').style.display = this.value === 'compatible' ? 'block' : 'none';">
                            <option value="native">原生接口 (Gemini Developer API)</option>
                            <option value="compatible">兼容接口 (OpenAI格式)</option>
                        </select>
                    </div>
                </div>

                <!-- Gemini 原生接口配置 -->
                <div class="gemini-native">
                    <div class="settings-group">
                        <h4>原生接口设置</h4>
                        <div class="form-group">
                            <label>API版本</label>
                            <select name="gemini.native.apiVersion">
                                <option value="v1">v1 (稳定版)</option>
                                <option value="v1alpha">v1alpha (预览版)</option>
                                <option value="v1beta">v1beta (测试版)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>基础URL</label>
                            <input type="url" name="gemini.native.baseUrl" value="https://generativelanguage.googleapis.com" readonly />
                            <small>Gemini Developer API 官方端点</small>
                        </div>
                        <div class="form-group">
                            <label>API密钥</label>
                            <input type="password" name="gemini.native.apiKey" placeholder="输入您的Gemini API密钥" />
                            <small>从 <a href="https://aistudio.google.com/app/apikey" target="_blank">Google AI Studio</a> 获取</small>
                        </div>
                        <div class="form-group">
                            <label>模型选择</label>
                            <select name="gemini.native.model">
                                <option value="gemini-1.5-pro">gemini-1.5-pro (推荐)</option>
                                <option value="gemini-1.5-flash">gemini-1.5-flash (快速)</option>
                                <option value="gemini-1.0-pro">gemini-1.0-pro (经典)</option>
                                <option value="gemini-pro-vision">gemini-pro-vision (视觉)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Gemini 兼容接口配置 -->
                <div class="gemini-compatible" style="display: none;">
                    <div class="settings-group">
                        <h4>兼容接口设置 (OpenAI格式)</h4>
                        <div class="form-group">
                            <label>代理端点</label>
                            <input type="url" name="gemini.compatible.endpoint" placeholder="https://your-proxy.com/v1" />
                            <small>支持OpenAI格式的Gemini代理服务</small>
                        </div>
                        <div class="form-group">
                            <label>API密钥</label>
                            <input type="password" name="gemini.compatible.apiKey" placeholder="代理服务的API密钥" />
                        </div>
                        <div class="form-group">
                            <label>模型映射</label>
                            <input type="text" name="gemini.compatible.model" value="gpt-3.5-turbo" placeholder="在代理中映射的模型名" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- OpenAI API 配置 -->
            <div class="provider-config provider-openai" style="display: none;">
                <div class="settings-group">
                    <h3>🤖 OpenAI API 配置</h3>
                    <div class="form-group">
                        <label>接口类型</label>
                        <select name="openai.interfaceType" onchange="this.closest('.provider-openai').querySelector('.openai-native').style.display = this.value === 'native' ? 'block' : 'none'; this.closest('.provider-openai').querySelector('.openai-compatible').style.display = this.value === 'compatible' ? 'block' : 'none';">
                            <option value="native">原生接口 (OpenAI官方)</option>
                            <option value="compatible">兼容接口 (第三方代理)</option>
                        </select>
                    </div>
                </div>

                <!-- OpenAI 原生接口配置 -->
                <div class="openai-native">
                    <div class="settings-group">
                        <h4>原生接口设置</h4>
                        <div class="form-group">
                            <label>基础URL</label>
                            <input type="url" name="openai.native.baseUrl" value="https://api.openai.com/v1" readonly />
                            <small>OpenAI 官方API端点</small>
                        </div>
                        <div class="form-group">
                            <label>API密钥</label>
                            <input type="password" name="openai.native.apiKey" placeholder="sk-..." />
                            <small>从 <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a> 获取</small>
                        </div>
                        <div class="form-group">
                            <label>组织ID (可选)</label>
                            <input type="text" name="openai.native.organization" placeholder="org-..." />
                        </div>
                        <div class="form-group">
                            <label>模型选择</label>
                            <select name="openai.native.model">
                                <option value="gpt-4">GPT-4 (推荐)</option>
                                <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                <option value="gpt-4o">GPT-4o (最新)</option>
                                <option value="gpt-4o-mini">GPT-4o Mini</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- OpenAI 兼容接口配置 -->
                <div class="openai-compatible" style="display: none;">
                    <div class="settings-group">
                        <h4>兼容接口设置</h4>
                        <div class="form-group">
                            <label>代理端点</label>
                            <input type="url" name="openai.compatible.endpoint" placeholder="https://your-proxy.com/v1" />
                            <small>兼容OpenAI格式的第三方服务</small>
                        </div>
                        <div class="form-group">
                            <label>API密钥</label>
                            <input type="password" name="openai.compatible.apiKey" placeholder="代理服务的API密钥" />
                        </div>
                        <div class="form-group">
                            <label>模型名称</label>
                            <input type="text" name="openai.compatible.model" placeholder="代理服务中的模型名" />
                        </div>
                        <div class="form-group">
                            <label>自定义请求头 (JSON)</label>
                            <textarea name="openai.compatible.headers" placeholder='{"Custom-Header": "value"}' rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通用生成参数 -->
            <div class="settings-group">
                <h3>⚙️ 生成参数</h3>
                <div class="form-group">
                    <label>温度 (0-2)</label>
                    <input type="range" name="apiConfig.temperature" min="0" max="2" step="0.1" value="0.7" />
                    <span class="range-value">0.7</span>
                </div>
                <div class="form-group">
                    <label>最大令牌数</label>
                    <input type="number" name="apiConfig.maxTokens" min="1" max="100000" step="100" value="2048" />
                </div>
                <div class="form-group">
                    <label>Top P (0-1)</label>
                    <input type="range" name="apiConfig.topP" min="0" max="1" step="0.01" value="0.9" />
                    <span class="range-value">0.9</span>
                </div>
                <div class="form-group">
                    <label>频率惩罚 (-2 到 2)</label>
                    <input type="range" name="apiConfig.frequencyPenalty" min="-2" max="2" step="0.1" value="0" />
                    <span class="range-value">0</span>
                </div>
            </div>

            <!-- 连接设置 -->
            <div class="settings-group">
                <h3>🔗 连接设置</h3>
                <div class="form-group">
                    <label>请求超时 (秒)</label>
                    <input type="number" name="apiConfig.timeout" min="5" max="300" step="5" value="30" />
                </div>
                <div class="form-group">
                    <label>重试次数</label>
                    <input type="number" name="apiConfig.retryCount" min="0" max="10" step="1" value="3" />
                </div>
                <div class="form-group">
                    <label>重试延迟 (毫秒)</label>
                    <input type="number" name="apiConfig.retryDelay" min="100" max="5000" step="100" value="1000" />
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-primary" data-action="test-connection">🔍 测试连接</button>
                    <button type="button" class="btn btn-secondary" data-action="load-models">📋 加载模型列表</button>
                </div>
            </div>

            <!-- API状态 -->
            <div class="settings-group">
                <h3>📊 API状态</h3>
                <div class="status-info">
                    <div class="status-item">
                        <span>连接状态:</span>
                        <span class="status-value" data-status="connection">未测试</span>
                    </div>
                    <div class="status-item">
                        <span>请求统计:</span>
                        <span class="status-value" data-status="stats">0/0</span>
                    </div>
                    <div class="status-item">
                        <span>最后测试:</span>
                        <span class="status-value" data-status="lastTest">从未</span>
                    </div>
                </div>
            </div>
            </div> <!-- 关闭 api-config-content -->
        `;
    }

    /**
     * 创建主题设置面板
     */
    createThemePanel() {
        return `
            <div class="settings-group">
                <h3>🎨 主题预览选择</h3>
                <p class="theme-description">选择您喜欢的主题风格，点击预览图即可应用</p>

                <div class="theme-gallery">
                    ${this.createThemePreviewGrid()}
                </div>
            </div>

            <div class="settings-group">
                <h3>🎯 当前主题设置</h3>
                <div class="current-theme-info">
                    <div class="form-group">
                        <label>当前主题</label>
                        <input type="text" name="theme.current" readonly value="默认深色" />
                    </div>
                    <div class="form-group">
                        <label>主题描述</label>
                        <textarea name="theme.description" readonly>经典深色主题，适合长时间使用，保护视力</textarea>
                    </div>
                </div>
            </div>

            <div class="settings-group custom-theme-group" style="display: none;">
                <h3>🛠️ 自定义主题</h3>
                <div class="color-picker-group">
                    <div class="form-group">
                        <label>主色调</label>
                        <input type="color" name="theme.custom.primary" value="#007bff" />
                    </div>
                    <div class="form-group">
                        <label>背景色</label>
                        <input type="color" name="theme.custom.background" value="#1a1a1a" />
                    </div>
                    <div class="form-group">
                        <label>文字色</label>
                        <input type="color" name="theme.custom.text" value="#ffffff" />
                    </div>
                    <div class="form-group">
                        <label>边框色</label>
                        <input type="color" name="theme.custom.border" value="#333333" />
                    </div>
                </div>

                <div class="theme-preview">
                    <h4>实时预览</h4>
                    <div class="preview-box custom-preview">
                        <div class="preview-header">示例标题</div>
                        <div class="preview-content">示例内容文字</div>
                        <div class="preview-button">示例按钮</div>
                    </div>
                </div>
            </div>

            <div class="settings-group">
                <h3>📝 字体设置</h3>
                <div class="form-group">
                    <label>字体大小</label>
                    <select name="theme.fontSize">
                        <option value="small">小 (12px)</option>
                        <option value="medium" selected>中 (14px)</option>
                        <option value="large">大 (16px)</option>
                        <option value="xlarge">特大 (18px)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>字体族</label>
                    <select name="theme.fontFamily">
                        <option value="system" selected>系统默认</option>
                        <option value="serif">衬线字体</option>
                        <option value="sans-serif">无衬线字体</option>
                        <option value="monospace">等宽字体</option>
                        <option value="custom">自定义字体</option>
                    </select>
                </div>
            </div>
        `;
    }

    /**
     * 创建面板管理面板
     */
    createPanelsPanel() {
        return `
            <div class="settings-group">
                <h3>面板管理</h3>
                <div class="panels-list">
                    <div class="panel-item">
                        <div class="panel-info">
                            <span class="panel-name">角色信息面板</span>
                            <span class="panel-status enabled">已启用</span>
                        </div>
                        <div class="panel-actions">
                            <button class="btn btn-small" data-action="edit-panel" data-panel="character">编辑</button>
                            <button class="btn btn-small" data-action="toggle-panel" data-panel="character">禁用</button>
                        </div>
                    </div>
                    <div class="panel-item">
                        <div class="panel-info">
                            <span class="panel-name">聊天统计面板</span>
                            <span class="panel-status enabled">已启用</span>
                        </div>
                        <div class="panel-actions">
                            <button class="btn btn-small" data-action="edit-panel" data-panel="stats">编辑</button>
                            <button class="btn btn-small" data-action="toggle-panel" data-panel="stats">禁用</button>
                        </div>
                    </div>
                </div>
                
                <div class="add-panel-section">
                    <button class="btn btn-primary" data-action="add-panel">添加新面板</button>
                </div>
            </div>
            
            <div class="settings-group">
                <h3>面板布局</h3>
                <div class="form-group">
                    <label>面板排列方式</label>
                    <select name="panels.layout">
                        <option value="vertical">垂直排列</option>
                        <option value="horizontal">水平排列</option>
                        <option value="grid">网格布局</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>面板间距 (px)</label>
                    <input type="number" name="panels.spacing" min="0" max="50" step="1" />
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="panels.resizable" />
                        <span>允许调整面板大小</span>
                    </label>
                </div>
            </div>
        `;
    }

    /**
     * 创建高级设置面板
     */
    createAdvancedPanel() {
        return `
            <div class="settings-group">
                <h3>数据管理</h3>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="dataManagement.autoBackup" />
                        <span>自动备份数据</span>
                    </label>
                </div>
                <div class="form-group">
                    <label>同步间隔 (秒)</label>
                    <input type="number" name="dataManagement.syncInterval" min="10" max="300" step="10" />
                </div>
                <div class="form-group">
                    <label>最大备份数量</label>
                    <input type="number" name="dataManagement.maxBackups" min="1" max="50" step="1" />
                </div>
            </div>
            
            <div class="settings-group">
                <h3>性能设置</h3>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="performance.enableCache" />
                        <span>启用缓存</span>
                    </label>
                </div>
                <div class="form-group">
                    <label>缓存大小限制 (MB)</label>
                    <input type="number" name="performance.cacheLimit" min="1" max="100" step="1" />
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="performance.lazyLoad" />
                        <span>启用懒加载</span>
                    </label>
                </div>
            </div>
            
            <div class="settings-group">
                <h3>调试设置</h3>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="debug.enabled" />
                        <span>启用调试模式</span>
                    </label>
                </div>
                <div class="form-group">
                    <label>日志级别</label>
                    <select name="debug.logLevel">
                        <option value="error">错误</option>
                        <option value="warn">警告</option>
                        <option value="info">信息</option>
                        <option value="debug">调试</option>
                    </select>
                </div>
            </div>
            
            <div class="settings-group danger-zone">
                <h3>危险操作</h3>
                <div class="form-group">
                    <button class="btn btn-danger" data-action="clear-cache">清除所有缓存</button>
                    <button class="btn btn-danger" data-action="reset-all">重置所有设置</button>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        try {
            // 模态框事件
            this.modal.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                
                switch (action) {
                    case 'close':
                    case 'cancel':
                        this.hide();
                        break;
                    case 'save':
                        this.saveSettings();
                        break;
                    case 'reset':
                        this.resetSettings();
                        break;
                    case 'export':
                        this.exportSettings();
                        break;
                    case 'import':
                        this.importSettings();
                        break;
                    case 'test-api':
                        this.testAPIConnection();
                        break;
                    case 'load-models':
                        this.loadAPIModels();
                        break;
                }
            });
            
            // 标签页切换
            this.modal.addEventListener('click', (e) => {
                if (e.target.classList.contains('tab-btn')) {
                    this.switchTab(e.target.dataset.tab);
                }
            });
            
            // 表单变更事件
            this.modal.addEventListener('change', (e) => {
                this.handleFormChange(e);
            });
            
            // 范围输入实时更新
            this.modal.addEventListener('input', (e) => {
                if (e.target.type === 'range') {
                    const valueSpan = e.target.nextElementSibling;
                    if (valueSpan && valueSpan.classList.contains('range-value')) {
                        valueSpan.textContent = e.target.value;
                    }
                }
            });
            
            console.log('[InfoBarSettings] 🔗 事件绑定完成');
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 绑定事件失败:', error);
            throw error;
        }
    }

    /**
     * 显示设置界面
     */
    async show() {
        try {
            if (!this.initialized) {
                await this.init();
            }
            
            // 加载最新设置
            await this.loadSettings();
            
            // 显示模态框
            this.modal.style.display = 'flex';
            this.visible = true;
            
            // 触发显示事件
            if (this.eventSystem) {
                this.eventSystem.emit('ui:show', {
                    component: 'InfoBarSettings',
                    timestamp: Date.now()
                });
            }
            
            console.log('[InfoBarSettings] 👁️ 设置界面已显示');
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 显示界面失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 隐藏设置界面
     */
    hide() {
        try {
            this.modal.style.display = 'none';
            this.visible = false;
            
            // 触发隐藏事件
            if (this.eventSystem) {
                this.eventSystem.emit('ui:hide', {
                    component: 'InfoBarSettings',
                    timestamp: Date.now()
                });
            }
            
            console.log('[InfoBarSettings] 👁️ 设置界面已隐藏');
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 隐藏界面失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        try {
            // 更新标签按钮状态
            this.modal.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.tab === tabName);
            });
            
            // 更新面板显示状态
            this.modal.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.toggle('active', panel.dataset.panel === tabName);
            });
            
            this.currentTab = tabName;
            
            console.log(`[InfoBarSettings] 📑 切换到标签页: ${tabName}`);
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 切换标签页失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 加载设置到表单
     */
    async loadSettings() {
        try {
            console.log('[InfoBarSettings] 📥 开始加载设置...');

            if (!this.configManager) {
                throw new Error('配置管理器未初始化');
            }

            const configs = await this.configManager.getAllConfigs();
            
            // 填充表单
            for (const [key, value] of Object.entries(configs)) {
                this.setFormValue(key, value);
            }
            
            // 更新API状态
            if (this.apiIntegration) {
                this.updateAPIStatus();
            }
            
            console.log('[InfoBarSettings] ✅ 设置加载完成');
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 加载设置失败:', error);
            this.handleError(error);
        }
    }

    /**
     * 设置表单值
     */
    setFormValue(name, value) {
        try {
            const element = this.modal.querySelector(`[name="${name}"]`);
            
            if (!element) {
                return;
            }
            
            if (element.type === 'checkbox') {
                element.checked = Boolean(value);
            } else if (element.type === 'range') {
                element.value = value;
                const valueSpan = element.nextElementSibling;
                if (valueSpan && valueSpan.classList.contains('range-value')) {
                    valueSpan.textContent = value;
                }
            } else {
                element.value = value || '';
            }
            
        } catch (error) {
            console.error(`[InfoBarSettings] ❌ 设置表单值失败 (${name}):`, error);
        }
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            console.log('[InfoBarSettings] 💾 开始保存设置...');
            
            // 收集表单数据
            const formData = this.collectFormData();
            
            // 批量保存配置
            await this.configManager.setConfigs(formData);
            
            // 如果API配置有变化，更新API集成
            if (this.hasAPIConfigChanged(formData)) {
                await this.apiIntegration.updateConfig(formData.apiConfig || {});
            }
            
            // 显示成功消息
            this.showMessage('设置保存成功', 'success');
            
            // 隐藏界面
            setTimeout(() => {
                this.hide();
            }, 1000);
            
            console.log('[InfoBarSettings] ✅ 设置保存完成');
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 保存设置失败:', error);
            this.showMessage('保存设置失败: ' + error.message, 'error');
            this.handleError(error);
        }
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const formData = {};
        
        // 获取所有表单元素
        const elements = this.modal.querySelectorAll('input, select, textarea');
        
        elements.forEach(element => {
            const name = element.name;
            if (!name) return;
            
            let value;
            if (element.type === 'checkbox') {
                value = element.checked;
            } else if (element.type === 'number' || element.type === 'range') {
                value = parseFloat(element.value) || 0;
            } else {
                value = element.value;
            }
            
            // 处理嵌套属性
            if (name.includes('.')) {
                this.setNestedProperty(formData, name, value);
            } else {
                formData[name] = value;
            }
        });
        
        return formData;
    }

    /**
     * 设置嵌套属性
     */
    setNestedProperty(obj, path, value) {
        const keys = path.split('.');
        let current = obj;
        
        for (let i = 0; i < keys.length - 1; i++) {
            if (!current[keys[i]]) {
                current[keys[i]] = {};
            }
            current = current[keys[i]];
        }
        
        current[keys[keys.length - 1]] = value;
    }

    /**
     * 检查API配置是否有变化
     */
    hasAPIConfigChanged(formData) {
        return Object.keys(formData).some(key => key.startsWith('apiConfig.'));
    }

    /**
     * 测试API连接
     */
    async testAPIConnection() {
        try {
            console.log('[InfoBarSettings] 🔍 开始测试API连接...');
            
            // 显示测试中状态
            this.updateConnectionStatus('testing', '测试中...');
            
            const result = await this.apiIntegration.testConnection();
            
            if (result.success) {
                this.updateConnectionStatus('success', '连接成功');
                this.showMessage('API连接测试成功', 'success');
            } else {
                this.updateConnectionStatus('error', '连接失败');
                this.showMessage('API连接测试失败: ' + result.error, 'error');
            }
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 测试API连接失败:', error);
            this.updateConnectionStatus('error', '测试异常');
            this.showMessage('API连接测试异常: ' + error.message, 'error');
        }
    }

    /**
     * 加载API模型
     */
    async loadAPIModels() {
        try {
            console.log('[InfoBarSettings] 📋 开始加载API模型...');
            
            const models = await this.apiIntegration.loadModels();
            const modelSelect = this.modal.querySelector('[name="apiConfig.model"]');
            
            // 清空现有选项
            modelSelect.innerHTML = '<option value="">选择模型...</option>';
            
            // 添加模型选项
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.name;
                option.title = model.description;
                modelSelect.appendChild(option);
            });
            
            this.showMessage(`成功加载 ${models.length} 个模型`, 'success');
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 加载API模型失败:', error);
            this.showMessage('加载模型失败: ' + error.message, 'error');
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(status, message) {
        const statusElement = this.modal.querySelector('[data-status="connection"]');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `status-value status-${status}`;
        }
    }

    /**
     * 更新API状态
     */
    updateAPIStatus() {
        if (!this.apiIntegration) return;
        
        const stats = this.apiIntegration.getStats();
        const statsElement = this.modal.querySelector('[data-status="stats"]');
        
        if (statsElement) {
            statsElement.textContent = `${stats.success}/${stats.total} (${stats.successRate})`;
        }
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        
        // 添加到模态框
        const modalContent = this.modal.querySelector('.modal-content');
        modalContent.insertBefore(messageEl, modalContent.firstChild);
        
        // 自动移除
        setTimeout(() => {
            messageEl.remove();
        }, 3000);
    }

    /**
     * 重置设置
     */
    async resetSettings() {
        try {
            if (!confirm('确定要重置所有设置到默认值吗？此操作不可撤销。')) {
                return;
            }
            
            await this.configManager.resetConfig();
            await this.loadSettings();
            
            this.showMessage('设置已重置到默认值', 'success');
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 重置设置失败:', error);
            this.showMessage('重置设置失败: ' + error.message, 'error');
        }
    }

    /**
     * 导出设置
     */
    async exportSettings() {
        try {
            const exportData = await this.configManager.exportConfigs();
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `info-bar-settings-${Date.now()}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            
            this.showMessage('设置导出成功', 'success');
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 导出设置失败:', error);
            this.showMessage('导出设置失败: ' + error.message, 'error');
        }
    }

    /**
     * 导入设置
     */
    async importSettings() {
        try {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;
                
                try {
                    const text = await file.text();
                    const importData = JSON.parse(text);
                    
                    await this.configManager.importConfigs(importData);
                    await this.loadSettings();
                    
                    this.showMessage('设置导入成功', 'success');
                    
                } catch (error) {
                    console.error('[InfoBarSettings] ❌ 导入设置失败:', error);
                    this.showMessage('导入设置失败: ' + error.message, 'error');
                }
            };
            
            input.click();
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 导入设置失败:', error);
            this.showMessage('导入设置失败: ' + error.message, 'error');
        }
    }

    /**
     * 处理表单变更
     */
    handleFormChange(e) {
        try {
            // 主题切换特殊处理
            if (e.target.name === 'theme.current') {
                const customGroup = this.modal.querySelector('.custom-theme-group');
                if (customGroup) {
                    customGroup.style.display = e.target.value === 'custom' ? 'block' : 'none';
                }
            }
            
            // 实时预览主题变化
            if (e.target.name && e.target.name.startsWith('theme.custom.')) {
                this.updateThemePreview();
            }
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 处理表单变更失败:', error);
        }
    }

    /**
     * 更新主题预览
     */
    updateThemePreview() {
        try {
            const previewBox = this.modal.querySelector('.preview-box');
            if (!previewBox) return;
            
            const customColors = {
                primary: this.modal.querySelector('[name="theme.custom.primary"]')?.value,
                background: this.modal.querySelector('[name="theme.custom.background"]')?.value,
                text: this.modal.querySelector('[name="theme.custom.text"]')?.value,
                border: this.modal.querySelector('[name="theme.custom.border"]')?.value
            };
            
            // 应用预览样式
            previewBox.style.backgroundColor = customColors.background;
            previewBox.style.color = customColors.text;
            previewBox.style.borderColor = customColors.border;
            
            const previewButton = previewBox.querySelector('.preview-button');
            if (previewButton) {
                previewButton.style.backgroundColor = customColors.primary;
            }
            
        } catch (error) {
            console.error('[InfoBarSettings] ❌ 更新主题预览失败:', error);
        }
    }

    /**
     * 创建个人信息面板
     */
    createPersonalPanel() {
        return `
            <div class="content-header">
                <h3>个人信息配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="personal-info-toggle" checked />
                    <label for="personal-info-toggle" class="switch-slider"></label>
                </div>
            </div>

            <div class="content-body">
                <!-- 个人信息卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">👤</div>
                        <div class="card-title">个人信息</div>
                        <div class="card-subtitle">角色自身的基础信息</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">8/52 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <!-- 基础信息 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="name-checkbox" name="personal.name.enabled" checked />
                                <label for="name-checkbox" class="checkbox-label">姓名</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="age-checkbox" name="personal.age.enabled" checked />
                                <label for="age-checkbox" class="checkbox-label">年龄</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="gender-checkbox" name="personal.gender.enabled" checked />
                                <label for="gender-checkbox" class="checkbox-label">性别</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="occupation-checkbox" name="personal.occupation.enabled" checked />
                                <label for="occupation-checkbox" class="checkbox-label">职业</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="height-checkbox" name="personal.height.enabled" />
                                <label for="height-checkbox" class="checkbox-label">身高</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="weight-checkbox" name="personal.weight.enabled" />
                                <label for="weight-checkbox" class="checkbox-label">体重</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="blood-type-checkbox" name="personal.bloodType.enabled" />
                                <label for="blood-type-checkbox" class="checkbox-label">血型</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="zodiac-checkbox" name="personal.zodiac.enabled" />
                                <label for="zodiac-checkbox" class="checkbox-label">星座</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="birthday-checkbox" name="personal.birthday.enabled" />
                                <label for="birthday-checkbox" class="checkbox-label">生日</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="birthplace-checkbox" name="personal.birthplace.enabled" />
                                <label for="birthplace-checkbox" class="checkbox-label">出生地</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="nationality-checkbox" name="personal.nationality.enabled" />
                                <label for="nationality-checkbox" class="checkbox-label">国籍</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="ethnicity-checkbox" name="personal.ethnicity.enabled" />
                                <label for="ethnicity-checkbox" class="checkbox-label">民族</label>
                            </div>
                        </div>
                    </div>

                    <!-- 外观特征 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="hair-color-checkbox" name="personal.hairColor.enabled" />
                                <label for="hair-color-checkbox" class="checkbox-label">发色</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="hair-style-checkbox" name="personal.hairStyle.enabled" />
                                <label for="hair-style-checkbox" class="checkbox-label">发型</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="eye-color-checkbox" name="personal.eyeColor.enabled" />
                                <label for="eye-color-checkbox" class="checkbox-label">眼色</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="skin-color-checkbox" name="personal.skinColor.enabled" />
                                <label for="skin-color-checkbox" class="checkbox-label">肤色</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="body-type-checkbox" name="personal.bodyType.enabled" />
                                <label for="body-type-checkbox" class="checkbox-label">体型</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="facial-features-checkbox" name="personal.facialFeatures.enabled" />
                                <label for="facial-features-checkbox" class="checkbox-label">面部特征</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="scars-checkbox" name="personal.scars.enabled" />
                                <label for="scars-checkbox" class="checkbox-label">疤痕</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tattoos-checkbox" name="personal.tattoos.enabled" />
                                <label for="tattoos-checkbox" class="checkbox-label">纹身</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="accessories-checkbox" name="personal.accessories.enabled" />
                                <label for="accessories-checkbox" class="checkbox-label">饰品</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="clothing-style-checkbox" name="personal.clothingStyle.enabled" />
                                <label for="clothing-style-checkbox" class="checkbox-label">服装风格</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="appearance-checkbox" name="personal.appearance.enabled" checked />
                                <label for="appearance-checkbox" class="checkbox-label">外观描述</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="voice-checkbox" name="personal.voice.enabled" />
                                <label for="voice-checkbox" class="checkbox-label">声音特征</label>
                            </div>
                        </div>
                    </div>

                    <!-- 性格特质 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="personality-checkbox" name="personal.personality.enabled" checked />
                                <label for="personality-checkbox" class="checkbox-label">性格</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="temperament-checkbox" name="personal.temperament.enabled" />
                                <label for="temperament-checkbox" class="checkbox-label">气质</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="attitude-checkbox" name="personal.attitude.enabled" />
                                <label for="attitude-checkbox" class="checkbox-label">态度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="values-checkbox" name="personal.values.enabled" />
                                <label for="values-checkbox" class="checkbox-label">价值观</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="beliefs-checkbox" name="personal.beliefs.enabled" />
                                <label for="beliefs-checkbox" class="checkbox-label">信仰</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fears-checkbox" name="personal.fears.enabled" />
                                <label for="fears-checkbox" class="checkbox-label">恐惧</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="dreams-checkbox" name="personal.dreams.enabled" />
                                <label for="dreams-checkbox" class="checkbox-label">梦想</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="goals-checkbox" name="personal.goals.enabled" />
                                <label for="goals-checkbox" class="checkbox-label">目标</label>
                            </div>
                        </div>
                    </div>

                    <!-- 能力属性 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="intelligence-checkbox" name="personal.intelligence.enabled" />
                                <label for="intelligence-checkbox" class="checkbox-label">智力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="strength-checkbox" name="personal.strength.enabled" />
                                <label for="strength-checkbox" class="checkbox-label">体力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="charisma-checkbox" name="personal.charisma.enabled" />
                                <label for="charisma-checkbox" class="checkbox-label">魅力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="luck-checkbox" name="personal.luck.enabled" />
                                <label for="luck-checkbox" class="checkbox-label">运气</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="perception-checkbox" name="personal.perception.enabled" />
                                <label for="perception-checkbox" class="checkbox-label">感知</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="willpower-checkbox" name="personal.willpower.enabled" />
                                <label for="willpower-checkbox" class="checkbox-label">意志力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="reaction-speed-checkbox" name="personal.reactionSpeed.enabled" />
                                <label for="reaction-speed-checkbox" class="checkbox-label">反应速度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="learning-ability-checkbox" name="personal.learningAbility.enabled" />
                                <label for="learning-ability-checkbox" class="checkbox-label">学习能力</label>
                            </div>
                        </div>
                    </div>

                    <!-- 社会关系 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="family-background-checkbox" name="personal.familyBackground.enabled" />
                                <label for="family-background-checkbox" class="checkbox-label">家庭背景</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="education-checkbox" name="personal.education.enabled" />
                                <label for="education-checkbox" class="checkbox-label">教育经历</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="work-experience-checkbox" name="personal.workExperience.enabled" />
                                <label for="work-experience-checkbox" class="checkbox-label">工作经历</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="income-checkbox" name="personal.income.enabled" />
                                <label for="income-checkbox" class="checkbox-label">收入</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="social-status-checkbox" name="personal.socialStatus.enabled" />
                                <label for="social-status-checkbox" class="checkbox-label">社会地位</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="relationships-checkbox" name="personal.relationships.enabled" />
                                <label for="relationships-checkbox" class="checkbox-label">人际关系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="love-status-checkbox" name="personal.loveStatus.enabled" />
                                <label for="love-status-checkbox" class="checkbox-label">恋爱状态</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="marital-status-checkbox" name="personal.maritalStatus.enabled" />
                                <label for="marital-status-checkbox" class="checkbox-label">婚姻状态</label>
                            </div>
                        </div>
                    </div>

                    <!-- 兴趣爱好 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="hobbies-checkbox" name="personal.hobbies.enabled" />
                                <label for="hobbies-checkbox" class="checkbox-label">兴趣爱好</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="sports-checkbox" name="personal.sports.enabled" />
                                <label for="sports-checkbox" class="checkbox-label">运动</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="music-checkbox" name="personal.music.enabled" />
                                <label for="music-checkbox" class="checkbox-label">音乐</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="art-checkbox" name="personal.art.enabled" />
                                <label for="art-checkbox" class="checkbox-label">艺术</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="reading-checkbox" name="personal.reading.enabled" />
                                <label for="reading-checkbox" class="checkbox-label">阅读</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="gaming-checkbox" name="personal.gaming.enabled" />
                                <label for="gaming-checkbox" class="checkbox-label">游戏</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="travel-checkbox" name="personal.travel.enabled" />
                                <label for="travel-checkbox" class="checkbox-label">旅行</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cooking-checkbox" name="personal.cooking.enabled" />
                                <label for="cooking-checkbox" class="checkbox-label">烹饪</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="skills-checkbox" name="personal.skills.enabled" />
                                <label for="skills-checkbox" class="checkbox-label">技能特长</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="languages-checkbox" name="personal.languages.enabled" />
                                <label for="languages-checkbox" class="checkbox-label">语言能力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="habits-checkbox" name="personal.habits.enabled" />
                                <label for="habits-checkbox" class="checkbox-label">生活习惯</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="health-status-checkbox" name="personal.healthStatus.enabled" />
                                <label for="health-status-checkbox" class="checkbox-label">健康状态</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮区域 -->
                <div class="action-buttons">
                    <button class="btn-action btn-all">全选</button>
                    <button class="btn-action btn-none">全不选</button>
                    <button class="btn-action btn-basic">基础信息</button>
                </div>
            </div>
        `;
    }

    /**
     * 创建交互对象面板
     */
    createInteractionPanel() {
        return `
            <div class="content-header">
                <h3>交互对象配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="interaction-toggle" name="interaction.enabled" checked />
                    <label for="interaction-toggle" class="switch-slider"></label>
                </div>
            </div>

            <div class="content-body">
                <!-- 交互对象卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">👥</div>
                        <div class="card-title">交互对象</div>
                        <div class="card-subtitle">角色交互和关系管理</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">12/52 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <!-- 基础信息 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-name-checkbox" name="interaction.name.enabled" checked />
                                <label for="interaction-name-checkbox" class="checkbox-label">对象名称</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-type-checkbox" name="interaction.type.enabled" checked />
                                <label for="interaction-type-checkbox" class="checkbox-label">对象类型</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-status-checkbox" name="interaction.status.enabled" checked />
                                <label for="interaction-status-checkbox" class="checkbox-label">在线状态</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-location-checkbox" name="interaction.location.enabled" />
                                <label for="interaction-location-checkbox" class="checkbox-label">所在位置</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-mood-checkbox" name="interaction.mood.enabled" />
                                <label for="interaction-mood-checkbox" class="checkbox-label">情绪状态</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-activity-checkbox" name="interaction.activity.enabled" />
                                <label for="interaction-activity-checkbox" class="checkbox-label">当前活动</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-availability-checkbox" name="interaction.availability.enabled" />
                                <label for="interaction-availability-checkbox" class="checkbox-label">可用性</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-priority-checkbox" name="interaction.priority.enabled" />
                                <label for="interaction-priority-checkbox" class="checkbox-label">优先级</label>
                            </div>
                        </div>
                    </div>

                    <!-- 关系信息 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-relationship-checkbox" name="interaction.relationship.enabled" checked />
                                <label for="interaction-relationship-checkbox" class="checkbox-label">关系类型</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-intimacy-checkbox" name="interaction.intimacy.enabled" checked />
                                <label for="interaction-intimacy-checkbox" class="checkbox-label">亲密度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-trust-checkbox" name="interaction.trust.enabled" />
                                <label for="interaction-trust-checkbox" class="checkbox-label">信任度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-friendship-checkbox" name="interaction.friendship.enabled" />
                                <label for="interaction-friendship-checkbox" class="checkbox-label">友好度</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-romance-checkbox" name="interaction.romance.enabled" />
                                <label for="interaction-romance-checkbox" class="checkbox-label">浪漫度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-respect-checkbox" name="interaction.respect.enabled" />
                                <label for="interaction-respect-checkbox" class="checkbox-label">尊重度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-dependency-checkbox" name="interaction.dependency.enabled" />
                                <label for="interaction-dependency-checkbox" class="checkbox-label">依赖度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-conflict-checkbox" name="interaction.conflict.enabled" />
                                <label for="interaction-conflict-checkbox" class="checkbox-label">冲突度</label>
                            </div>
                        </div>
                    </div>

                    <!-- 交互历史 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-history-checkbox" name="interaction.history.enabled" checked />
                                <label for="interaction-history-checkbox" class="checkbox-label">交互历史</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-frequency-checkbox" name="interaction.frequency.enabled" />
                                <label for="interaction-frequency-checkbox" class="checkbox-label">交互频率</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-duration-checkbox" name="interaction.duration.enabled" />
                                <label for="interaction-duration-checkbox" class="checkbox-label">交互时长</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-quality-checkbox" name="interaction.quality.enabled" />
                                <label for="interaction-quality-checkbox" class="checkbox-label">交互质量</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-topics-checkbox" name="interaction.topics.enabled" />
                                <label for="interaction-topics-checkbox" class="checkbox-label">话题记录</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-emotions-checkbox" name="interaction.emotions.enabled" />
                                <label for="interaction-emotions-checkbox" class="checkbox-label">情感变化</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-milestones-checkbox" name="interaction.milestones.enabled" />
                                <label for="interaction-milestones-checkbox" class="checkbox-label">重要节点</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-memories-checkbox" name="interaction.memories.enabled" />
                                <label for="interaction-memories-checkbox" class="checkbox-label">共同回忆</label>
                            </div>
                        </div>
                    </div>

                    <!-- 交互设置 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-auto-record-checkbox" name="interaction.autoRecord.enabled" checked />
                                <label for="interaction-auto-record-checkbox" class="checkbox-label">自动记录</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-notifications-checkbox" name="interaction.notifications.enabled" />
                                <label for="interaction-notifications-checkbox" class="checkbox-label">交互通知</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-analysis-checkbox" name="interaction.analysis.enabled" />
                                <label for="interaction-analysis-checkbox" class="checkbox-label">行为分析</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-suggestions-checkbox" name="interaction.suggestions.enabled" />
                                <label for="interaction-suggestions-checkbox" class="checkbox-label">建议提示</label>
                            </div>
                        </div>
                    </div>

                    <!-- 社交网络 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-network-checkbox" name="interaction.network.enabled" />
                                <label for="interaction-network-checkbox" class="checkbox-label">社交网络</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-groups-checkbox" name="interaction.groups.enabled" />
                                <label for="interaction-groups-checkbox" class="checkbox-label">群组关系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-influence-checkbox" name="interaction.influence.enabled" />
                                <label for="interaction-influence-checkbox" class="checkbox-label">影响力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-reputation-checkbox" name="interaction.reputation.enabled" />
                                <label for="interaction-reputation-checkbox" class="checkbox-label">声誉</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-alliances-checkbox" name="interaction.alliances.enabled" />
                                <label for="interaction-alliances-checkbox" class="checkbox-label">联盟关系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-rivalries-checkbox" name="interaction.rivalries.enabled" />
                                <label for="interaction-rivalries-checkbox" class="checkbox-label">竞争关系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-mentorship-checkbox" name="interaction.mentorship.enabled" />
                                <label for="interaction-mentorship-checkbox" class="checkbox-label">师徒关系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-hierarchy-checkbox" name="interaction.hierarchy.enabled" />
                                <label for="interaction-hierarchy-checkbox" class="checkbox-label">等级关系</label>
                            </div>
                        </div>
                    </div>

                    <!-- 交互偏好 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-communication-style-checkbox" name="interaction.communicationStyle.enabled" />
                                <label for="interaction-communication-style-checkbox" class="checkbox-label">沟通风格</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-preferred-topics-checkbox" name="interaction.preferredTopics.enabled" />
                                <label for="interaction-preferred-topics-checkbox" class="checkbox-label">偏好话题</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-avoided-topics-checkbox" name="interaction.avoidedTopics.enabled" />
                                <label for="interaction-avoided-topics-checkbox" class="checkbox-label">避免话题</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-boundaries-checkbox" name="interaction.boundaries.enabled" />
                                <label for="interaction-boundaries-checkbox" class="checkbox-label">交互边界</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-comfort-level-checkbox" name="interaction.comfortLevel.enabled" />
                                <label for="interaction-comfort-level-checkbox" class="checkbox-label">舒适度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-energy-level-checkbox" name="interaction.energyLevel.enabled" />
                                <label for="interaction-energy-level-checkbox" class="checkbox-label">活跃度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-response-time-checkbox" name="interaction.responseTime.enabled" />
                                <label for="interaction-response-time-checkbox" class="checkbox-label">响应时间</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-engagement-checkbox" name="interaction.engagement.enabled" />
                                <label for="interaction-engagement-checkbox" class="checkbox-label">参与度</label>
                            </div>
                        </div>
                    </div>

                    <!-- 特殊状态 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-special-events-checkbox" name="interaction.specialEvents.enabled" />
                                <label for="interaction-special-events-checkbox" class="checkbox-label">特殊事件</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-achievements-checkbox" name="interaction.achievements.enabled" />
                                <label for="interaction-achievements-checkbox" class="checkbox-label">成就记录</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-challenges-checkbox" name="interaction.challenges.enabled" />
                                <label for="interaction-challenges-checkbox" class="checkbox-label">挑战记录</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="interaction-growth-checkbox" name="interaction.growth.enabled" />
                                <label for="interaction-growth-checkbox" class="checkbox-label">成长轨迹</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建任务系统面板
     */
    createTasksPanel() {
        return `
            <div class="content-header">
                <h3>任务系统配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="tasks-toggle" name="tasks.enabled" checked />
                    <label for="tasks-toggle" class="switch-slider"></label>
                </div>
            </div>

            <div class="content-body">
                <!-- 任务系统卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">📋</div>
                        <div class="card-title">任务系统</div>
                        <div class="card-subtitle">任务管理和进度跟踪</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">15/52 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <!-- 任务基础 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-creation-checkbox" name="tasks.creation.enabled" checked />
                                <label for="tasks-creation-checkbox" class="checkbox-label">任务创建</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-editing-checkbox" name="tasks.editing.enabled" checked />
                                <label for="tasks-editing-checkbox" class="checkbox-label">任务编辑</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-deletion-checkbox" name="tasks.deletion.enabled" checked />
                                <label for="tasks-deletion-checkbox" class="checkbox-label">任务删除</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-completion-checkbox" name="tasks.completion.enabled" checked />
                                <label for="tasks-completion-checkbox" class="checkbox-label">任务完成</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-priority-checkbox" name="tasks.priority.enabled" />
                                <label for="tasks-priority-checkbox" class="checkbox-label">优先级设置</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-deadline-checkbox" name="tasks.deadline.enabled" />
                                <label for="tasks-deadline-checkbox" class="checkbox-label">截止时间</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-progress-checkbox" name="tasks.progress.enabled" />
                                <label for="tasks-progress-checkbox" class="checkbox-label">进度跟踪</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-status-checkbox" name="tasks.status.enabled" />
                                <label for="tasks-status-checkbox" class="checkbox-label">状态管理</label>
                            </div>
                        </div>
                    </div>

                    <!-- 任务分类 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-categories-checkbox" name="tasks.categories.enabled" />
                                <label for="tasks-categories-checkbox" class="checkbox-label">任务分类</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-tags-checkbox" name="tasks.tags.enabled" />
                                <label for="tasks-tags-checkbox" class="checkbox-label">标签系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-projects-checkbox" name="tasks.projects.enabled" />
                                <label for="tasks-projects-checkbox" class="checkbox-label">项目分组</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-milestones-checkbox" name="tasks.milestones.enabled" />
                                <label for="tasks-milestones-checkbox" class="checkbox-label">里程碑</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-subtasks-checkbox" name="tasks.subtasks.enabled" />
                                <label for="tasks-subtasks-checkbox" class="checkbox-label">子任务</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-dependencies-checkbox" name="tasks.dependencies.enabled" />
                                <label for="tasks-dependencies-checkbox" class="checkbox-label">任务依赖</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-templates-checkbox" name="tasks.templates.enabled" />
                                <label for="tasks-templates-checkbox" class="checkbox-label">任务模板</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-recurring-checkbox" name="tasks.recurring.enabled" />
                                <label for="tasks-recurring-checkbox" class="checkbox-label">重复任务</label>
                            </div>
                        </div>
                    </div>

                    <!-- 通知提醒 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-notifications-checkbox" name="tasks.notifications.enabled" checked />
                                <label for="tasks-notifications-checkbox" class="checkbox-label">任务通知</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-reminders-checkbox" name="tasks.reminders.enabled" />
                                <label for="tasks-reminders-checkbox" class="checkbox-label">提醒设置</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-alerts-checkbox" name="tasks.alerts.enabled" />
                                <label for="tasks-alerts-checkbox" class="checkbox-label">逾期警告</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-daily-summary-checkbox" name="tasks.dailySummary.enabled" />
                                <label for="tasks-daily-summary-checkbox" class="checkbox-label">每日总结</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-weekly-review-checkbox" name="tasks.weeklyReview.enabled" />
                                <label for="tasks-weekly-review-checkbox" class="checkbox-label">周度回顾</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-achievement-badges-checkbox" name="tasks.achievementBadges.enabled" />
                                <label for="tasks-achievement-badges-checkbox" class="checkbox-label">成就徽章</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-productivity-stats-checkbox" name="tasks.productivityStats.enabled" />
                                <label for="tasks-productivity-stats-checkbox" class="checkbox-label">效率统计</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-time-tracking-checkbox" name="tasks.timeTracking.enabled" />
                                <label for="tasks-time-tracking-checkbox" class="checkbox-label">时间跟踪</label>
                            </div>
                        </div>
                    </div>

                    <!-- 协作功能 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-assignment-checkbox" name="tasks.assignment.enabled" />
                                <label for="tasks-assignment-checkbox" class="checkbox-label">任务分配</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-collaboration-checkbox" name="tasks.collaboration.enabled" />
                                <label for="tasks-collaboration-checkbox" class="checkbox-label">协作功能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-comments-checkbox" name="tasks.comments.enabled" />
                                <label for="tasks-comments-checkbox" class="checkbox-label">任务评论</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-attachments-checkbox" name="tasks.attachments.enabled" />
                                <label for="tasks-attachments-checkbox" class="checkbox-label">文件附件</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-sharing-checkbox" name="tasks.sharing.enabled" />
                                <label for="tasks-sharing-checkbox" class="checkbox-label">任务分享</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-permissions-checkbox" name="tasks.permissions.enabled" />
                                <label for="tasks-permissions-checkbox" class="checkbox-label">权限管理</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-approval-checkbox" name="tasks.approval.enabled" />
                                <label for="tasks-approval-checkbox" class="checkbox-label">审批流程</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-delegation-checkbox" name="tasks.delegation.enabled" />
                                <label for="tasks-delegation-checkbox" class="checkbox-label">任务委派</label>
                            </div>
                        </div>
                    </div>

                    <!-- 视图和排序 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-list-view-checkbox" name="tasks.listView.enabled" checked />
                                <label for="tasks-list-view-checkbox" class="checkbox-label">列表视图</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-kanban-view-checkbox" name="tasks.kanbanView.enabled" />
                                <label for="tasks-kanban-view-checkbox" class="checkbox-label">看板视图</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-calendar-view-checkbox" name="tasks.calendarView.enabled" />
                                <label for="tasks-calendar-view-checkbox" class="checkbox-label">日历视图</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-gantt-view-checkbox" name="tasks.ganttView.enabled" />
                                <label for="tasks-gantt-view-checkbox" class="checkbox-label">甘特图</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-sorting-checkbox" name="tasks.sorting.enabled" checked />
                                <label for="tasks-sorting-checkbox" class="checkbox-label">排序功能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-filtering-checkbox" name="tasks.filtering.enabled" />
                                <label for="tasks-filtering-checkbox" class="checkbox-label">筛选功能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-search-checkbox" name="tasks.search.enabled" />
                                <label for="tasks-search-checkbox" class="checkbox-label">搜索功能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-grouping-checkbox" name="tasks.grouping.enabled" />
                                <label for="tasks-grouping-checkbox" class="checkbox-label">分组显示</label>
                            </div>
                        </div>
                    </div>

                    <!-- 数据管理 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-backup-checkbox" name="tasks.backup.enabled" />
                                <label for="tasks-backup-checkbox" class="checkbox-label">数据备份</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-export-checkbox" name="tasks.export.enabled" />
                                <label for="tasks-export-checkbox" class="checkbox-label">数据导出</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-import-checkbox" name="tasks.import.enabled" />
                                <label for="tasks-import-checkbox" class="checkbox-label">数据导入</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-sync-checkbox" name="tasks.sync.enabled" />
                                <label for="tasks-sync-checkbox" class="checkbox-label">云端同步</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-archive-checkbox" name="tasks.archive.enabled" />
                                <label for="tasks-archive-checkbox" class="checkbox-label">任务归档</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-history-checkbox" name="tasks.history.enabled" />
                                <label for="tasks-history-checkbox" class="checkbox-label">历史记录</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-versioning-checkbox" name="tasks.versioning.enabled" />
                                <label for="tasks-versioning-checkbox" class="checkbox-label">版本控制</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="tasks-recovery-checkbox" name="tasks.recovery.enabled" />
                                <label for="tasks-recovery-checkbox" class="checkbox-label">数据恢复</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建世界信息面板
     */
    createWorldPanel() {
        return `
            <div class="content-header">
                <h3>世界信息配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="world-toggle" name="world.enabled" checked />
                    <label for="world-toggle" class="switch-slider"></label>
                </div>
            </div>

            <div class="content-body">
                <!-- 世界信息卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">🌍</div>
                        <div class="card-title">世界信息</div>
                        <div class="card-subtitle">世界设定和环境管理</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">18/52 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <!-- 基础设定 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-name-checkbox" name="world.name.enabled" checked />
                                <label for="world-name-checkbox" class="checkbox-label">世界名称</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-type-checkbox" name="world.type.enabled" checked />
                                <label for="world-type-checkbox" class="checkbox-label">世界类型</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-genre-checkbox" name="world.genre.enabled" checked />
                                <label for="world-genre-checkbox" class="checkbox-label">世界风格</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-theme-checkbox" name="world.theme.enabled" />
                                <label for="world-theme-checkbox" class="checkbox-label">主题设定</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-description-checkbox" name="world.description.enabled" checked />
                                <label for="world-description-checkbox" class="checkbox-label">世界描述</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-history-checkbox" name="world.history.enabled" />
                                <label for="world-history-checkbox" class="checkbox-label">历史背景</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-mythology-checkbox" name="world.mythology.enabled" />
                                <label for="world-mythology-checkbox" class="checkbox-label">神话传说</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-lore-checkbox" name="world.lore.enabled" />
                                <label for="world-lore-checkbox" class="checkbox-label">世界观设定</label>
                            </div>
                        </div>
                    </div>

                    <!-- 地理环境 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-geography-checkbox" name="world.geography.enabled" checked />
                                <label for="world-geography-checkbox" class="checkbox-label">地理环境</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-climate-checkbox" name="world.climate.enabled" />
                                <label for="world-climate-checkbox" class="checkbox-label">气候条件</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-terrain-checkbox" name="world.terrain.enabled" />
                                <label for="world-terrain-checkbox" class="checkbox-label">地形地貌</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-biomes-checkbox" name="world.biomes.enabled" />
                                <label for="world-biomes-checkbox" class="checkbox-label">生态群落</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-locations-checkbox" name="world.locations.enabled" checked />
                                <label for="world-locations-checkbox" class="checkbox-label">重要地点</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-landmarks-checkbox" name="world.landmarks.enabled" />
                                <label for="world-landmarks-checkbox" class="checkbox-label">地标建筑</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-cities-checkbox" name="world.cities.enabled" />
                                <label for="world-cities-checkbox" class="checkbox-label">城市聚落</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-dungeons-checkbox" name="world.dungeons.enabled" />
                                <label for="world-dungeons-checkbox" class="checkbox-label">地下城</label>
                            </div>
                        </div>
                    </div>

                    <!-- 时间系统 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-time-checkbox" name="world.time.enabled" checked />
                                <label for="world-time-checkbox" class="checkbox-label">时间系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-calendar-checkbox" name="world.calendar.enabled" />
                                <label for="world-calendar-checkbox" class="checkbox-label">历法系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-seasons-checkbox" name="world.seasons.enabled" />
                                <label for="world-seasons-checkbox" class="checkbox-label">季节变化</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-day-night-checkbox" name="world.dayNight.enabled" />
                                <label for="world-day-night-checkbox" class="checkbox-label">昼夜循环</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-weather-checkbox" name="world.weather.enabled" />
                                <label for="world-weather-checkbox" class="checkbox-label">天气系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-events-checkbox" name="world.events.enabled" />
                                <label for="world-events-checkbox" class="checkbox-label">世界事件</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-festivals-checkbox" name="world.festivals.enabled" />
                                <label for="world-festivals-checkbox" class="checkbox-label">节日庆典</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-disasters-checkbox" name="world.disasters.enabled" />
                                <label for="world-disasters-checkbox" class="checkbox-label">自然灾害</label>
                            </div>
                        </div>
                    </div>

                    <!-- 社会文化 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-cultures-checkbox" name="world.cultures.enabled" />
                                <label for="world-cultures-checkbox" class="checkbox-label">文化体系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-languages-checkbox" name="world.languages.enabled" />
                                <label for="world-languages-checkbox" class="checkbox-label">语言系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-religions-checkbox" name="world.religions.enabled" />
                                <label for="world-religions-checkbox" class="checkbox-label">宗教信仰</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-customs-checkbox" name="world.customs.enabled" />
                                <label for="world-customs-checkbox" class="checkbox-label">风俗习惯</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-politics-checkbox" name="world.politics.enabled" />
                                <label for="world-politics-checkbox" class="checkbox-label">政治制度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-economy-checkbox" name="world.economy.enabled" />
                                <label for="world-economy-checkbox" class="checkbox-label">经济体系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-technology-checkbox" name="world.technology.enabled" />
                                <label for="world-technology-checkbox" class="checkbox-label">科技水平</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-magic-checkbox" name="world.magic.enabled" />
                                <label for="world-magic-checkbox" class="checkbox-label">魔法体系</label>
                            </div>
                        </div>
                    </div>

                    <!-- 生物种族 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-races-checkbox" name="world.races.enabled" />
                                <label for="world-races-checkbox" class="checkbox-label">种族设定</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-creatures-checkbox" name="world.creatures.enabled" />
                                <label for="world-creatures-checkbox" class="checkbox-label">生物群体</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-monsters-checkbox" name="world.monsters.enabled" />
                                <label for="world-monsters-checkbox" class="checkbox-label">怪物设定</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-npcs-checkbox" name="world.npcs.enabled" />
                                <label for="world-npcs-checkbox" class="checkbox-label">NPC管理</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-factions-checkbox" name="world.factions.enabled" />
                                <label for="world-factions-checkbox" class="checkbox-label">势力阵营</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-conflicts-checkbox" name="world.conflicts.enabled" />
                                <label for="world-conflicts-checkbox" class="checkbox-label">冲突矛盾</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-alliances-checkbox" name="world.alliances.enabled" />
                                <label for="world-alliances-checkbox" class="checkbox-label">联盟关系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-wars-checkbox" name="world.wars.enabled" />
                                <label for="world-wars-checkbox" class="checkbox-label">战争历史</label>
                            </div>
                        </div>
                    </div>

                    <!-- 资源物品 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-resources-checkbox" name="world.resources.enabled" />
                                <label for="world-resources-checkbox" class="checkbox-label">自然资源</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-materials-checkbox" name="world.materials.enabled" />
                                <label for="world-materials-checkbox" class="checkbox-label">材料物品</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-artifacts-checkbox" name="world.artifacts.enabled" />
                                <label for="world-artifacts-checkbox" class="checkbox-label">神器宝物</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-currency-checkbox" name="world.currency.enabled" />
                                <label for="world-currency-checkbox" class="checkbox-label">货币系统</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-trade-checkbox" name="world.trade.enabled" />
                                <label for="world-trade-checkbox" class="checkbox-label">贸易体系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-markets-checkbox" name="world.markets.enabled" />
                                <label for="world-markets-checkbox" class="checkbox-label">市场商店</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-guilds-checkbox" name="world.guilds.enabled" />
                                <label for="world-guilds-checkbox" class="checkbox-label">公会组织</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="world-transportation-checkbox" name="world.transportation.enabled" />
                                <label for="world-transportation-checkbox" class="checkbox-label">交通运输</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建组织信息面板
     */
    createOrganizationPanel() {
        return `
            <div class="content-header">
                <h3>组织信息配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="organization-toggle" name="organization.enabled" checked />
                    <label for="organization-toggle" class="switch-slider"></label>
                </div>
            </div>

            <div class="content-body">
                <!-- 组织信息卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">🏛️</div>
                        <div class="card-title">组织信息</div>
                        <div class="card-subtitle">组织管理和成员关系</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">16/52 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <!-- 基础信息 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-name-checkbox" name="organization.name.enabled" checked />
                                <label for="org-name-checkbox" class="checkbox-label">组织名称</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-type-checkbox" name="organization.type.enabled" checked />
                                <label for="org-type-checkbox" class="checkbox-label">组织类型</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-description-checkbox" name="organization.description.enabled" checked />
                                <label for="org-description-checkbox" class="checkbox-label">组织描述</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-purpose-checkbox" name="organization.purpose.enabled" />
                                <label for="org-purpose-checkbox" class="checkbox-label">组织目标</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-history-checkbox" name="organization.history.enabled" />
                                <label for="org-history-checkbox" class="checkbox-label">组织历史</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-founding-checkbox" name="organization.founding.enabled" />
                                <label for="org-founding-checkbox" class="checkbox-label">成立背景</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-motto-checkbox" name="organization.motto.enabled" />
                                <label for="org-motto-checkbox" class="checkbox-label">组织格言</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-values-checkbox" name="organization.values.enabled" />
                                <label for="org-values-checkbox" class="checkbox-label">核心价值</label>
                            </div>
                        </div>
                    </div>

                    <!-- 组织结构 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-hierarchy-checkbox" name="organization.hierarchy.enabled" checked />
                                <label for="org-hierarchy-checkbox" class="checkbox-label">等级制度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-departments-checkbox" name="organization.departments.enabled" />
                                <label for="org-departments-checkbox" class="checkbox-label">部门分工</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-leadership-checkbox" name="organization.leadership.enabled" />
                                <label for="org-leadership-checkbox" class="checkbox-label">领导层</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-council-checkbox" name="organization.council.enabled" />
                                <label for="org-council-checkbox" class="checkbox-label">议事会</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-positions-checkbox" name="organization.positions.enabled" checked />
                                <label for="org-positions-checkbox" class="checkbox-label">职位体系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-ranks-checkbox" name="organization.ranks.enabled" />
                                <label for="org-ranks-checkbox" class="checkbox-label">等级划分</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-promotion-checkbox" name="organization.promotion.enabled" />
                                <label for="org-promotion-checkbox" class="checkbox-label">晋升制度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-authority-checkbox" name="organization.authority.enabled" />
                                <label for="org-authority-checkbox" class="checkbox-label">权限管理</label>
                            </div>
                        </div>
                    </div>

                    <!-- 成员管理 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-members-checkbox" name="organization.members.enabled" checked />
                                <label for="org-members-checkbox" class="checkbox-label">成员名单</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-recruitment-checkbox" name="organization.recruitment.enabled" />
                                <label for="org-recruitment-checkbox" class="checkbox-label">招募制度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-training-checkbox" name="organization.training.enabled" />
                                <label for="org-training-checkbox" class="checkbox-label">培训体系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-evaluation-checkbox" name="organization.evaluation.enabled" />
                                <label for="org-evaluation-checkbox" class="checkbox-label">考核评估</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-rewards-checkbox" name="organization.rewards.enabled" />
                                <label for="org-rewards-checkbox" class="checkbox-label">奖励机制</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-punishment-checkbox" name="organization.punishment.enabled" />
                                <label for="org-punishment-checkbox" class="checkbox-label">惩罚制度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-benefits-checkbox" name="organization.benefits.enabled" />
                                <label for="org-benefits-checkbox" class="checkbox-label">福利待遇</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-retirement-checkbox" name="organization.retirement.enabled" />
                                <label for="org-retirement-checkbox" class="checkbox-label">退休制度</label>
                            </div>
                        </div>
                    </div>

                    <!-- 规章制度 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-rules-checkbox" name="organization.rules.enabled" />
                                <label for="org-rules-checkbox" class="checkbox-label">组织规章</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-code-checkbox" name="organization.code.enabled" />
                                <label for="org-code-checkbox" class="checkbox-label">行为准则</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-ethics-checkbox" name="organization.ethics.enabled" />
                                <label for="org-ethics-checkbox" class="checkbox-label">道德标准</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-discipline-checkbox" name="organization.discipline.enabled" />
                                <label for="org-discipline-checkbox" class="checkbox-label">纪律要求</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-procedures-checkbox" name="organization.procedures.enabled" />
                                <label for="org-procedures-checkbox" class="checkbox-label">工作流程</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-protocols-checkbox" name="organization.protocols.enabled" />
                                <label for="org-protocols-checkbox" class="checkbox-label">操作规程</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-standards-checkbox" name="organization.standards.enabled" />
                                <label for="org-standards-checkbox" class="checkbox-label">质量标准</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-compliance-checkbox" name="organization.compliance.enabled" />
                                <label for="org-compliance-checkbox" class="checkbox-label">合规要求</label>
                            </div>
                        </div>
                    </div>

                    <!-- 对外关系 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-allies-checkbox" name="organization.allies.enabled" />
                                <label for="org-allies-checkbox" class="checkbox-label">盟友组织</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-enemies-checkbox" name="organization.enemies.enabled" />
                                <label for="org-enemies-checkbox" class="checkbox-label">敌对势力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-neutral-checkbox" name="organization.neutral.enabled" />
                                <label for="org-neutral-checkbox" class="checkbox-label">中立关系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-partnerships-checkbox" name="organization.partnerships.enabled" />
                                <label for="org-partnerships-checkbox" class="checkbox-label">合作伙伴</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-reputation-checkbox" name="organization.reputation.enabled" />
                                <label for="org-reputation-checkbox" class="checkbox-label">声誉影响</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-influence-checkbox" name="organization.influence.enabled" />
                                <label for="org-influence-checkbox" class="checkbox-label">势力范围</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-diplomacy-checkbox" name="organization.diplomacy.enabled" />
                                <label for="org-diplomacy-checkbox" class="checkbox-label">外交政策</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-treaties-checkbox" name="organization.treaties.enabled" />
                                <label for="org-treaties-checkbox" class="checkbox-label">条约协议</label>
                            </div>
                        </div>
                    </div>

                    <!-- 资源管理 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-finances-checkbox" name="organization.finances.enabled" />
                                <label for="org-finances-checkbox" class="checkbox-label">财务管理</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-assets-checkbox" name="organization.assets.enabled" />
                                <label for="org-assets-checkbox" class="checkbox-label">资产清单</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-facilities-checkbox" name="organization.facilities.enabled" />
                                <label for="org-facilities-checkbox" class="checkbox-label">设施场所</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-equipment-checkbox" name="organization.equipment.enabled" />
                                <label for="org-equipment-checkbox" class="checkbox-label">装备器材</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-technology-checkbox" name="organization.technology.enabled" />
                                <label for="org-technology-checkbox" class="checkbox-label">技术资源</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-knowledge-checkbox" name="organization.knowledge.enabled" />
                                <label for="org-knowledge-checkbox" class="checkbox-label">知识库</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-archives-checkbox" name="organization.archives.enabled" />
                                <label for="org-archives-checkbox" class="checkbox-label">档案记录</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="org-secrets-checkbox" name="organization.secrets.enabled" />
                                <label for="org-secrets-checkbox" class="checkbox-label">机密信息</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建资讯内容面板
     */
    createNewsPanel() {
        return `
            <div class="content-header">
                <h3>资讯内容配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="news-toggle" name="news.enabled" checked />
                    <label for="news-toggle" class="switch-slider"></label>
                </div>
            </div>

            <div class="content-body">
                <!-- 资讯内容卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">📰</div>
                        <div class="card-title">资讯内容</div>
                        <div class="card-subtitle">信息管理和内容分发</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">20/52 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <!-- 内容类型 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-breaking-checkbox" name="news.breaking.enabled" checked />
                                <label for="news-breaking-checkbox" class="checkbox-label">突发新闻</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-politics-checkbox" name="news.politics.enabled" checked />
                                <label for="news-politics-checkbox" class="checkbox-label">政治新闻</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-economy-checkbox" name="news.economy.enabled" checked />
                                <label for="news-economy-checkbox" class="checkbox-label">经济资讯</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-social-checkbox" name="news.social.enabled" />
                                <label for="news-social-checkbox" class="checkbox-label">社会新闻</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-military-checkbox" name="news.military.enabled" />
                                <label for="news-military-checkbox" class="checkbox-label">军事动态</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-technology-checkbox" name="news.technology.enabled" />
                                <label for="news-technology-checkbox" class="checkbox-label">科技资讯</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-culture-checkbox" name="news.culture.enabled" />
                                <label for="news-culture-checkbox" class="checkbox-label">文化艺术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-sports-checkbox" name="news.sports.enabled" />
                                <label for="news-sports-checkbox" class="checkbox-label">体育赛事</label>
                            </div>
                        </div>
                    </div>

                    <!-- 信息来源 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-official-checkbox" name="news.official.enabled" checked />
                                <label for="news-official-checkbox" class="checkbox-label">官方消息</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-media-checkbox" name="news.media.enabled" />
                                <label for="news-media-checkbox" class="checkbox-label">媒体报道</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-rumors-checkbox" name="news.rumors.enabled" />
                                <label for="news-rumors-checkbox" class="checkbox-label">传言消息</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-insider-checkbox" name="news.insider.enabled" />
                                <label for="news-insider-checkbox" class="checkbox-label">内部消息</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-witness-checkbox" name="news.witness.enabled" />
                                <label for="news-witness-checkbox" class="checkbox-label">目击报告</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-intelligence-checkbox" name="news.intelligence.enabled" />
                                <label for="news-intelligence-checkbox" class="checkbox-label">情报信息</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-leaked-checkbox" name="news.leaked.enabled" />
                                <label for="news-leaked-checkbox" class="checkbox-label">泄露文件</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-anonymous-checkbox" name="news.anonymous.enabled" />
                                <label for="news-anonymous-checkbox" class="checkbox-label">匿名爆料</label>
                            </div>
                        </div>
                    </div>

                    <!-- 内容管理 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-creation-checkbox" name="news.creation.enabled" checked />
                                <label for="news-creation-checkbox" class="checkbox-label">内容创建</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-editing-checkbox" name="news.editing.enabled" />
                                <label for="news-editing-checkbox" class="checkbox-label">内容编辑</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-review-checkbox" name="news.review.enabled" />
                                <label for="news-review-checkbox" class="checkbox-label">内容审核</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-publishing-checkbox" name="news.publishing.enabled" />
                                <label for="news-publishing-checkbox" class="checkbox-label">内容发布</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-archiving-checkbox" name="news.archiving.enabled" />
                                <label for="news-archiving-checkbox" class="checkbox-label">内容归档</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-deletion-checkbox" name="news.deletion.enabled" />
                                <label for="news-deletion-checkbox" class="checkbox-label">内容删除</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-backup-checkbox" name="news.backup.enabled" />
                                <label for="news-backup-checkbox" class="checkbox-label">内容备份</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-versioning-checkbox" name="news.versioning.enabled" />
                                <label for="news-versioning-checkbox" class="checkbox-label">版本控制</label>
                            </div>
                        </div>
                    </div>

                    <!-- 分发渠道 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-broadcast-checkbox" name="news.broadcast.enabled" />
                                <label for="news-broadcast-checkbox" class="checkbox-label">广播发布</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-newsletter-checkbox" name="news.newsletter.enabled" />
                                <label for="news-newsletter-checkbox" class="checkbox-label">新闻简报</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-alerts-checkbox" name="news.alerts.enabled" />
                                <label for="news-alerts-checkbox" class="checkbox-label">紧急通知</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-digest-checkbox" name="news.digest.enabled" />
                                <label for="news-digest-checkbox" class="checkbox-label">新闻摘要</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-social-media-checkbox" name="news.socialMedia.enabled" />
                                <label for="news-social-media-checkbox" class="checkbox-label">社交媒体</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-forums-checkbox" name="news.forums.enabled" />
                                <label for="news-forums-checkbox" class="checkbox-label">论坛发布</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-messaging-checkbox" name="news.messaging.enabled" />
                                <label for="news-messaging-checkbox" class="checkbox-label">消息推送</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-email-checkbox" name="news.email.enabled" />
                                <label for="news-email-checkbox" class="checkbox-label">邮件通知</label>
                            </div>
                        </div>
                    </div>

                    <!-- 互动功能 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-comments-checkbox" name="news.comments.enabled" />
                                <label for="news-comments-checkbox" class="checkbox-label">评论功能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-likes-checkbox" name="news.likes.enabled" />
                                <label for="news-likes-checkbox" class="checkbox-label">点赞功能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-sharing-checkbox" name="news.sharing.enabled" />
                                <label for="news-sharing-checkbox" class="checkbox-label">分享功能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-bookmarks-checkbox" name="news.bookmarks.enabled" />
                                <label for="news-bookmarks-checkbox" class="checkbox-label">收藏功能</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-ratings-checkbox" name="news.ratings.enabled" />
                                <label for="news-ratings-checkbox" class="checkbox-label">评分系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-polls-checkbox" name="news.polls.enabled" />
                                <label for="news-polls-checkbox" class="checkbox-label">投票调查</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-discussions-checkbox" name="news.discussions.enabled" />
                                <label for="news-discussions-checkbox" class="checkbox-label">讨论区</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-feedback-checkbox" name="news.feedback.enabled" />
                                <label for="news-feedback-checkbox" class="checkbox-label">反馈系统</label>
                            </div>
                        </div>
                    </div>

                    <!-- 数据分析 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-analytics-checkbox" name="news.analytics.enabled" />
                                <label for="news-analytics-checkbox" class="checkbox-label">数据分析</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-metrics-checkbox" name="news.metrics.enabled" />
                                <label for="news-metrics-checkbox" class="checkbox-label">指标统计</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-trends-checkbox" name="news.trends.enabled" />
                                <label for="news-trends-checkbox" class="checkbox-label">趋势分析</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-reports-checkbox" name="news.reports.enabled" />
                                <label for="news-reports-checkbox" class="checkbox-label">报告生成</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-monitoring-checkbox" name="news.monitoring.enabled" />
                                <label for="news-monitoring-checkbox" class="checkbox-label">监控系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-alerts-system-checkbox" name="news.alertsSystem.enabled" />
                                <label for="news-alerts-system-checkbox" class="checkbox-label">预警系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-automation-checkbox" name="news.automation.enabled" />
                                <label for="news-automation-checkbox" class="checkbox-label">自动化处理</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="news-ai-analysis-checkbox" name="news.aiAnalysis.enabled" />
                                <label for="news-ai-analysis-checkbox" class="checkbox-label">AI分析</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建背包仓库面板
     */
    createInventoryPanel() {
        return `
            <div class="content-header">
                <h3>背包仓库配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="inventory-toggle" name="inventory.enabled" checked />
                    <label for="inventory-toggle" class="switch-slider"></label>
                </div>
            </div>

            <div class="content-body">
                <!-- 背包仓库卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">🎒</div>
                        <div class="card-title">背包仓库</div>
                        <div class="card-subtitle">物品管理和存储系统</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">22/52 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <!-- 基础功能 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-storage-checkbox" name="inventory.storage.enabled" checked />
                                <label for="inventory-storage-checkbox" class="checkbox-label">物品存储</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-retrieval-checkbox" name="inventory.retrieval.enabled" checked />
                                <label for="inventory-retrieval-checkbox" class="checkbox-label">物品取出</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-organization-checkbox" name="inventory.organization.enabled" checked />
                                <label for="inventory-organization-checkbox" class="checkbox-label">物品整理</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-search-checkbox" name="inventory.search.enabled" />
                                <label for="inventory-search-checkbox" class="checkbox-label">物品搜索</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-sorting-checkbox" name="inventory.sorting.enabled" />
                                <label for="inventory-sorting-checkbox" class="checkbox-label">自动排序</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-filtering-checkbox" name="inventory.filtering.enabled" />
                                <label for="inventory-filtering-checkbox" class="checkbox-label">物品筛选</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-categories-checkbox" name="inventory.categories.enabled" />
                                <label for="inventory-categories-checkbox" class="checkbox-label">分类管理</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-tags-checkbox" name="inventory.tags.enabled" />
                                <label for="inventory-tags-checkbox" class="checkbox-label">标签系统</label>
                            </div>
                        </div>
                    </div>

                    <!-- 物品类型 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-weapons-checkbox" name="inventory.weapons.enabled" checked />
                                <label for="inventory-weapons-checkbox" class="checkbox-label">武器装备</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-armor-checkbox" name="inventory.armor.enabled" checked />
                                <label for="inventory-armor-checkbox" class="checkbox-label">防具护甲</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-accessories-checkbox" name="inventory.accessories.enabled" />
                                <label for="inventory-accessories-checkbox" class="checkbox-label">饰品配件</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-consumables-checkbox" name="inventory.consumables.enabled" />
                                <label for="inventory-consumables-checkbox" class="checkbox-label">消耗品</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-materials-checkbox" name="inventory.materials.enabled" />
                                <label for="inventory-materials-checkbox" class="checkbox-label">材料物品</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-tools-checkbox" name="inventory.tools.enabled" />
                                <label for="inventory-tools-checkbox" class="checkbox-label">工具器械</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-books-checkbox" name="inventory.books.enabled" />
                                <label for="inventory-books-checkbox" class="checkbox-label">书籍文献</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-treasures-checkbox" name="inventory.treasures.enabled" />
                                <label for="inventory-treasures-checkbox" class="checkbox-label">珍宝收藏</label>
                            </div>
                        </div>
                    </div>

                    <!-- 存储管理 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-capacity-checkbox" name="inventory.capacity.enabled" checked />
                                <label for="inventory-capacity-checkbox" class="checkbox-label">容量管理</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-weight-checkbox" name="inventory.weight.enabled" />
                                <label for="inventory-weight-checkbox" class="checkbox-label">重量限制</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-stacking-checkbox" name="inventory.stacking.enabled" />
                                <label for="inventory-stacking-checkbox" class="checkbox-label">物品堆叠</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-expansion-checkbox" name="inventory.expansion.enabled" />
                                <label for="inventory-expansion-checkbox" class="checkbox-label">容量扩展</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-compartments-checkbox" name="inventory.compartments.enabled" />
                                <label for="inventory-compartments-checkbox" class="checkbox-label">分隔区域</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-protection-checkbox" name="inventory.protection.enabled" />
                                <label for="inventory-protection-checkbox" class="checkbox-label">物品保护</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-durability-checkbox" name="inventory.durability.enabled" />
                                <label for="inventory-durability-checkbox" class="checkbox-label">耐久度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-repair-checkbox" name="inventory.repair.enabled" />
                                <label for="inventory-repair-checkbox" class="checkbox-label">修理系统</label>
                            </div>
                        </div>
                    </div>

                    <!-- 交易功能 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-trading-checkbox" name="inventory.trading.enabled" />
                                <label for="inventory-trading-checkbox" class="checkbox-label">物品交易</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-selling-checkbox" name="inventory.selling.enabled" />
                                <label for="inventory-selling-checkbox" class="checkbox-label">物品出售</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-buying-checkbox" name="inventory.buying.enabled" />
                                <label for="inventory-buying-checkbox" class="checkbox-label">物品购买</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-auction-checkbox" name="inventory.auction.enabled" />
                                <label for="inventory-auction-checkbox" class="checkbox-label">拍卖系统</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-gifting-checkbox" name="inventory.gifting.enabled" />
                                <label for="inventory-gifting-checkbox" class="checkbox-label">物品赠送</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-lending-checkbox" name="inventory.lending.enabled" />
                                <label for="inventory-lending-checkbox" class="checkbox-label">物品借贷</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-sharing-checkbox" name="inventory.sharing.enabled" />
                                <label for="inventory-sharing-checkbox" class="checkbox-label">物品共享</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-banking-checkbox" name="inventory.banking.enabled" />
                                <label for="inventory-banking-checkbox" class="checkbox-label">银行存储</label>
                            </div>
                        </div>
                    </div>

                    <!-- 制作系统 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-crafting-checkbox" name="inventory.crafting.enabled" />
                                <label for="inventory-crafting-checkbox" class="checkbox-label">物品制作</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-recipes-checkbox" name="inventory.recipes.enabled" />
                                <label for="inventory-recipes-checkbox" class="checkbox-label">配方管理</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-enhancement-checkbox" name="inventory.enhancement.enabled" />
                                <label for="inventory-enhancement-checkbox" class="checkbox-label">物品强化</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-enchanting-checkbox" name="inventory.enchanting.enabled" />
                                <label for="inventory-enchanting-checkbox" class="checkbox-label">附魔系统</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-upgrading-checkbox" name="inventory.upgrading.enabled" />
                                <label for="inventory-upgrading-checkbox" class="checkbox-label">物品升级</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-combining-checkbox" name="inventory.combining.enabled" />
                                <label for="inventory-combining-checkbox" class="checkbox-label">物品合成</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-dismantling-checkbox" name="inventory.dismantling.enabled" />
                                <label for="inventory-dismantling-checkbox" class="checkbox-label">物品分解</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-recycling-checkbox" name="inventory.recycling.enabled" />
                                <label for="inventory-recycling-checkbox" class="checkbox-label">回收利用</label>
                            </div>
                        </div>
                    </div>

                    <!-- 高级功能 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-automation-checkbox" name="inventory.automation.enabled" />
                                <label for="inventory-automation-checkbox" class="checkbox-label">自动化管理</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-ai-sorting-checkbox" name="inventory.aiSorting.enabled" />
                                <label for="inventory-ai-sorting-checkbox" class="checkbox-label">智能排序</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-recommendations-checkbox" name="inventory.recommendations.enabled" />
                                <label for="inventory-recommendations-checkbox" class="checkbox-label">推荐系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-analytics-checkbox" name="inventory.analytics.enabled" />
                                <label for="inventory-analytics-checkbox" class="checkbox-label">使用分析</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-backup-checkbox" name="inventory.backup.enabled" />
                                <label for="inventory-backup-checkbox" class="checkbox-label">数据备份</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-sync-checkbox" name="inventory.sync.enabled" />
                                <label for="inventory-sync-checkbox" class="checkbox-label">云端同步</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-security-checkbox" name="inventory.security.enabled" />
                                <label for="inventory-security-checkbox" class="checkbox-label">安全保护</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="inventory-history-checkbox" name="inventory.history.enabled" />
                                <label for="inventory-history-checkbox" class="checkbox-label">操作历史</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建能力系统面板
     */
    createAbilitiesPanel() {
        return `
            <div class="content-header">
                <h3>能力系统配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="abilities-toggle" name="abilities.enabled" checked />
                    <label for="abilities-toggle" class="switch-slider"></label>
                </div>
            </div>

            <div class="content-body">
                <!-- 能力系统卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">⚡</div>
                        <div class="card-title">能力系统</div>
                        <div class="card-subtitle">技能和属性管理系统</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">25/52 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <!-- 基础属性 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-strength-checkbox" name="abilities.strength.enabled" checked />
                                <label for="abilities-strength-checkbox" class="checkbox-label">力量属性</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-agility-checkbox" name="abilities.agility.enabled" checked />
                                <label for="abilities-agility-checkbox" class="checkbox-label">敏捷属性</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-intelligence-checkbox" name="abilities.intelligence.enabled" checked />
                                <label for="abilities-intelligence-checkbox" class="checkbox-label">智力属性</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-constitution-checkbox" name="abilities.constitution.enabled" />
                                <label for="abilities-constitution-checkbox" class="checkbox-label">体质属性</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-wisdom-checkbox" name="abilities.wisdom.enabled" />
                                <label for="abilities-wisdom-checkbox" class="checkbox-label">智慧属性</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-charisma-checkbox" name="abilities.charisma.enabled" />
                                <label for="abilities-charisma-checkbox" class="checkbox-label">魅力属性</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-luck-checkbox" name="abilities.luck.enabled" />
                                <label for="abilities-luck-checkbox" class="checkbox-label">幸运属性</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-perception-checkbox" name="abilities.perception.enabled" />
                                <label for="abilities-perception-checkbox" class="checkbox-label">感知属性</label>
                            </div>
                        </div>
                    </div>

                    <!-- 战斗技能 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-swordsmanship-checkbox" name="abilities.swordsmanship.enabled" checked />
                                <label for="abilities-swordsmanship-checkbox" class="checkbox-label">剑术技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-archery-checkbox" name="abilities.archery.enabled" />
                                <label for="abilities-archery-checkbox" class="checkbox-label">弓箭技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-magic-checkbox" name="abilities.magic.enabled" checked />
                                <label for="abilities-magic-checkbox" class="checkbox-label">魔法技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-defense-checkbox" name="abilities.defense.enabled" />
                                <label for="abilities-defense-checkbox" class="checkbox-label">防御技能</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-martial-arts-checkbox" name="abilities.martialArts.enabled" />
                                <label for="abilities-martial-arts-checkbox" class="checkbox-label">武术技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-stealth-checkbox" name="abilities.stealth.enabled" />
                                <label for="abilities-stealth-checkbox" class="checkbox-label">潜行技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-tactics-checkbox" name="abilities.tactics.enabled" />
                                <label for="abilities-tactics-checkbox" class="checkbox-label">战术技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-healing-checkbox" name="abilities.healing.enabled" />
                                <label for="abilities-healing-checkbox" class="checkbox-label">治疗技能</label>
                            </div>
                        </div>
                    </div>

                    <!-- 生活技能 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-crafting-checkbox" name="abilities.crafting.enabled" />
                                <label for="abilities-crafting-checkbox" class="checkbox-label">制作技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-cooking-checkbox" name="abilities.cooking.enabled" />
                                <label for="abilities-cooking-checkbox" class="checkbox-label">烹饪技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-farming-checkbox" name="abilities.farming.enabled" />
                                <label for="abilities-farming-checkbox" class="checkbox-label">农业技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-mining-checkbox" name="abilities.mining.enabled" />
                                <label for="abilities-mining-checkbox" class="checkbox-label">采矿技能</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-fishing-checkbox" name="abilities.fishing.enabled" />
                                <label for="abilities-fishing-checkbox" class="checkbox-label">钓鱼技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-hunting-checkbox" name="abilities.hunting.enabled" />
                                <label for="abilities-hunting-checkbox" class="checkbox-label">狩猎技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-trading-checkbox" name="abilities.trading.enabled" />
                                <label for="abilities-trading-checkbox" class="checkbox-label">交易技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-negotiation-checkbox" name="abilities.negotiation.enabled" />
                                <label for="abilities-negotiation-checkbox" class="checkbox-label">谈判技能</label>
                            </div>
                        </div>
                    </div>

                    <!-- 知识技能 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-research-checkbox" name="abilities.research.enabled" />
                                <label for="abilities-research-checkbox" class="checkbox-label">研究技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-investigation-checkbox" name="abilities.investigation.enabled" />
                                <label for="abilities-investigation-checkbox" class="checkbox-label">调查技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-languages-checkbox" name="abilities.languages.enabled" />
                                <label for="abilities-languages-checkbox" class="checkbox-label">语言技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-history-checkbox" name="abilities.history.enabled" />
                                <label for="abilities-history-checkbox" class="checkbox-label">历史知识</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-medicine-checkbox" name="abilities.medicine.enabled" />
                                <label for="abilities-medicine-checkbox" class="checkbox-label">医学知识</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-alchemy-checkbox" name="abilities.alchemy.enabled" />
                                <label for="abilities-alchemy-checkbox" class="checkbox-label">炼金术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-engineering-checkbox" name="abilities.engineering.enabled" />
                                <label for="abilities-engineering-checkbox" class="checkbox-label">工程学</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-astronomy-checkbox" name="abilities.astronomy.enabled" />
                                <label for="abilities-astronomy-checkbox" class="checkbox-label">天文学</label>
                            </div>
                        </div>
                    </div>

                    <!-- 社交技能 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-persuasion-checkbox" name="abilities.persuasion.enabled" />
                                <label for="abilities-persuasion-checkbox" class="checkbox-label">说服技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-deception-checkbox" name="abilities.deception.enabled" />
                                <label for="abilities-deception-checkbox" class="checkbox-label">欺骗技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-intimidation-checkbox" name="abilities.intimidation.enabled" />
                                <label for="abilities-intimidation-checkbox" class="checkbox-label">威吓技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-performance-checkbox" name="abilities.performance.enabled" />
                                <label for="abilities-performance-checkbox" class="checkbox-label">表演技能</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-leadership-checkbox" name="abilities.leadership.enabled" />
                                <label for="abilities-leadership-checkbox" class="checkbox-label">领导技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-empathy-checkbox" name="abilities.empathy.enabled" />
                                <label for="abilities-empathy-checkbox" class="checkbox-label">共情技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-insight-checkbox" name="abilities.insight.enabled" />
                                <label for="abilities-insight-checkbox" class="checkbox-label">洞察技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-networking-checkbox" name="abilities.networking.enabled" />
                                <label for="abilities-networking-checkbox" class="checkbox-label">人脉技能</label>
                            </div>
                        </div>
                    </div>

                    <!-- 特殊能力 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-telepathy-checkbox" name="abilities.telepathy.enabled" />
                                <label for="abilities-telepathy-checkbox" class="checkbox-label">心灵感应</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-telekinesis-checkbox" name="abilities.telekinesis.enabled" />
                                <label for="abilities-telekinesis-checkbox" class="checkbox-label">念力移物</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-precognition-checkbox" name="abilities.precognition.enabled" />
                                <label for="abilities-precognition-checkbox" class="checkbox-label">预知能力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-shapeshifting-checkbox" name="abilities.shapeshifting.enabled" />
                                <label for="abilities-shapeshifting-checkbox" class="checkbox-label">变形能力</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-invisibility-checkbox" name="abilities.invisibility.enabled" />
                                <label for="abilities-invisibility-checkbox" class="checkbox-label">隐身能力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-flight-checkbox" name="abilities.flight.enabled" />
                                <label for="abilities-flight-checkbox" class="checkbox-label">飞行能力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-regeneration-checkbox" name="abilities.regeneration.enabled" />
                                <label for="abilities-regeneration-checkbox" class="checkbox-label">再生能力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="abilities-immortality-checkbox" name="abilities.immortality.enabled" />
                                <label for="abilities-immortality-checkbox" class="checkbox-label">不朽能力</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建剧情面板
     */
    createPlotPanel() {
        return `
            <div class="content-header">
                <h3>剧情面板配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="plot-toggle" name="plot.enabled" checked />
                    <label for="plot-toggle" class="switch-slider"></label>
                </div>
            </div>

            <div class="content-body">
                <!-- 剧情面板卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">📖</div>
                        <div class="card-title">剧情面板</div>
                        <div class="card-subtitle">故事情节和叙事管理</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">18/52 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <!-- 剧情结构 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-main-story-checkbox" name="plot.mainStory.enabled" checked />
                                <label for="plot-main-story-checkbox" class="checkbox-label">主线剧情</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-side-quests-checkbox" name="plot.sideQuests.enabled" checked />
                                <label for="plot-side-quests-checkbox" class="checkbox-label">支线任务</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-subplots-checkbox" name="plot.subplots.enabled" checked />
                                <label for="plot-subplots-checkbox" class="checkbox-label">子情节</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-backstory-checkbox" name="plot.backstory.enabled" />
                                <label for="plot-backstory-checkbox" class="checkbox-label">背景故事</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-prologue-checkbox" name="plot.prologue.enabled" />
                                <label for="plot-prologue-checkbox" class="checkbox-label">序章</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-epilogue-checkbox" name="plot.epilogue.enabled" />
                                <label for="plot-epilogue-checkbox" class="checkbox-label">尾声</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-flashbacks-checkbox" name="plot.flashbacks.enabled" />
                                <label for="plot-flashbacks-checkbox" class="checkbox-label">回忆片段</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-foreshadowing-checkbox" name="plot.foreshadowing.enabled" />
                                <label for="plot-foreshadowing-checkbox" class="checkbox-label">伏笔铺垫</label>
                            </div>
                        </div>
                    </div>

                    <!-- 剧情阶段 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-exposition-checkbox" name="plot.exposition.enabled" checked />
                                <label for="plot-exposition-checkbox" class="checkbox-label">开端</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-rising-action-checkbox" name="plot.risingAction.enabled" />
                                <label for="plot-rising-action-checkbox" class="checkbox-label">发展</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-climax-checkbox" name="plot.climax.enabled" />
                                <label for="plot-climax-checkbox" class="checkbox-label">高潮</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-falling-action-checkbox" name="plot.fallingAction.enabled" />
                                <label for="plot-falling-action-checkbox" class="checkbox-label">下降</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-resolution-checkbox" name="plot.resolution.enabled" />
                                <label for="plot-resolution-checkbox" class="checkbox-label">结局</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-denouement-checkbox" name="plot.denouement.enabled" />
                                <label for="plot-denouement-checkbox" class="checkbox-label">收尾</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-cliffhanger-checkbox" name="plot.cliffhanger.enabled" />
                                <label for="plot-cliffhanger-checkbox" class="checkbox-label">悬念结尾</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-twist-checkbox" name="plot.twist.enabled" />
                                <label for="plot-twist-checkbox" class="checkbox-label">剧情转折</label>
                            </div>
                        </div>
                    </div>

                    <!-- 角色发展 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-character-arc-checkbox" name="plot.characterArc.enabled" />
                                <label for="plot-character-arc-checkbox" class="checkbox-label">角色成长</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-relationships-checkbox" name="plot.relationships.enabled" />
                                <label for="plot-relationships-checkbox" class="checkbox-label">关系发展</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-motivations-checkbox" name="plot.motivations.enabled" />
                                <label for="plot-motivations-checkbox" class="checkbox-label">动机驱动</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-conflicts-checkbox" name="plot.conflicts.enabled" />
                                <label for="plot-conflicts-checkbox" class="checkbox-label">冲突矛盾</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-internal-conflicts-checkbox" name="plot.internalConflicts.enabled" />
                                <label for="plot-internal-conflicts-checkbox" class="checkbox-label">内心冲突</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-external-conflicts-checkbox" name="plot.externalConflicts.enabled" />
                                <label for="plot-external-conflicts-checkbox" class="checkbox-label">外部冲突</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-moral-dilemmas-checkbox" name="plot.moralDilemmas.enabled" />
                                <label for="plot-moral-dilemmas-checkbox" class="checkbox-label">道德困境</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-sacrifices-checkbox" name="plot.sacrifices.enabled" />
                                <label for="plot-sacrifices-checkbox" class="checkbox-label">牺牲选择</label>
                            </div>
                        </div>
                    </div>

                    <!-- 叙事技巧 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-dialogue-checkbox" name="plot.dialogue.enabled" />
                                <label for="plot-dialogue-checkbox" class="checkbox-label">对话系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-narration-checkbox" name="plot.narration.enabled" />
                                <label for="plot-narration-checkbox" class="checkbox-label">叙述描写</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-monologue-checkbox" name="plot.monologue.enabled" />
                                <label for="plot-monologue-checkbox" class="checkbox-label">内心独白</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-symbolism-checkbox" name="plot.symbolism.enabled" />
                                <label for="plot-symbolism-checkbox" class="checkbox-label">象征隐喻</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-themes-checkbox" name="plot.themes.enabled" />
                                <label for="plot-themes-checkbox" class="checkbox-label">主题表达</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-mood-checkbox" name="plot.mood.enabled" />
                                <label for="plot-mood-checkbox" class="checkbox-label">氛围营造</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-tone-checkbox" name="plot.tone.enabled" />
                                <label for="plot-tone-checkbox" class="checkbox-label">语调风格</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-pacing-checkbox" name="plot.pacing.enabled" />
                                <label for="plot-pacing-checkbox" class="checkbox-label">节奏控制</label>
                            </div>
                        </div>
                    </div>

                    <!-- 互动元素 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-choices-checkbox" name="plot.choices.enabled" />
                                <label for="plot-choices-checkbox" class="checkbox-label">选择分支</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-consequences-checkbox" name="plot.consequences.enabled" />
                                <label for="plot-consequences-checkbox" class="checkbox-label">后果影响</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-branching-checkbox" name="plot.branching.enabled" />
                                <label for="plot-branching-checkbox" class="checkbox-label">分支剧情</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-multiple-endings-checkbox" name="plot.multipleEndings.enabled" />
                                <label for="plot-multiple-endings-checkbox" class="checkbox-label">多重结局</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-player-agency-checkbox" name="plot.playerAgency.enabled" />
                                <label for="plot-player-agency-checkbox" class="checkbox-label">玩家主导</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-emergent-narrative-checkbox" name="plot.emergentNarrative.enabled" />
                                <label for="plot-emergent-narrative-checkbox" class="checkbox-label">涌现叙事</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-procedural-generation-checkbox" name="plot.proceduralGeneration.enabled" />
                                <label for="plot-procedural-generation-checkbox" class="checkbox-label">程序生成</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-adaptive-storytelling-checkbox" name="plot.adaptiveStorytelling.enabled" />
                                <label for="plot-adaptive-storytelling-checkbox" class="checkbox-label">自适应叙事</label>
                            </div>
                        </div>
                    </div>

                    <!-- 管理功能 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-timeline-checkbox" name="plot.timeline.enabled" />
                                <label for="plot-timeline-checkbox" class="checkbox-label">时间线管理</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-notes-checkbox" name="plot.notes.enabled" />
                                <label for="plot-notes-checkbox" class="checkbox-label">剧情笔记</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-bookmarks-checkbox" name="plot.bookmarks.enabled" />
                                <label for="plot-bookmarks-checkbox" class="checkbox-label">重要节点</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-save-states-checkbox" name="plot.saveStates.enabled" />
                                <label for="plot-save-states-checkbox" class="checkbox-label">存档管理</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-auto-save-checkbox" name="plot.autoSave.enabled" />
                                <label for="plot-auto-save-checkbox" class="checkbox-label">自动保存</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-export-checkbox" name="plot.export.enabled" />
                                <label for="plot-export-checkbox" class="checkbox-label">导出功能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-import-checkbox" name="plot.import.enabled" />
                                <label for="plot-import-checkbox" class="checkbox-label">导入功能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="plot-analytics-checkbox" name="plot.analytics.enabled" />
                                <label for="plot-analytics-checkbox" class="checkbox-label">剧情分析</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建修仙世界面板
     */
    createCultivationPanel() {
        return `
            <div class="content-header">
                <h3>修仙世界配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="cultivation-toggle" name="cultivation.enabled" checked />
                    <label for="cultivation-toggle" class="switch-slider"></label>
                </div>
            </div>

            <div class="content-body">
                <!-- 修仙世界卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">⚡</div>
                        <div class="card-title">修仙世界</div>
                        <div class="card-subtitle">仙侠修炼体系设定</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">22/52 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <!-- 修炼境界 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-qi-refining-checkbox" name="cultivation.qiRefining.enabled" checked />
                                <label for="cultivation-qi-refining-checkbox" class="checkbox-label">炼气期</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-foundation-checkbox" name="cultivation.foundation.enabled" checked />
                                <label for="cultivation-foundation-checkbox" class="checkbox-label">筑基期</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-golden-core-checkbox" name="cultivation.goldenCore.enabled" checked />
                                <label for="cultivation-golden-core-checkbox" class="checkbox-label">金丹期</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-nascent-soul-checkbox" name="cultivation.nascentSoul.enabled" />
                                <label for="cultivation-nascent-soul-checkbox" class="checkbox-label">元婴期</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-soul-transformation-checkbox" name="cultivation.soulTransformation.enabled" />
                                <label for="cultivation-soul-transformation-checkbox" class="checkbox-label">化神期</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-void-refinement-checkbox" name="cultivation.voidRefinement.enabled" />
                                <label for="cultivation-void-refinement-checkbox" class="checkbox-label">炼虚期</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-body-integration-checkbox" name="cultivation.bodyIntegration.enabled" />
                                <label for="cultivation-body-integration-checkbox" class="checkbox-label">合体期</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-mahayana-checkbox" name="cultivation.mahayana.enabled" />
                                <label for="cultivation-mahayana-checkbox" class="checkbox-label">大乘期</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-tribulation-checkbox" name="cultivation.tribulation.enabled" />
                                <label for="cultivation-tribulation-checkbox" class="checkbox-label">渡劫期</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-immortal-checkbox" name="cultivation.immortal.enabled" />
                                <label for="cultivation-immortal-checkbox" class="checkbox-label">仙人境</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-true-immortal-checkbox" name="cultivation.trueImmortal.enabled" />
                                <label for="cultivation-true-immortal-checkbox" class="checkbox-label">真仙境</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-golden-immortal-checkbox" name="cultivation.goldenImmortal.enabled" />
                                <label for="cultivation-golden-immortal-checkbox" class="checkbox-label">金仙境</label>
                            </div>
                        </div>
                    </div>

                    <!-- 功法体系 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-breathing-technique-checkbox" name="cultivation.breathingTechnique.enabled" checked />
                                <label for="cultivation-breathing-technique-checkbox" class="checkbox-label">吐纳功法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-body-refining-checkbox" name="cultivation.bodyRefining.enabled" />
                                <label for="cultivation-body-refining-checkbox" class="checkbox-label">炼体功法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-soul-cultivation-checkbox" name="cultivation.soulCultivation.enabled" />
                                <label for="cultivation-soul-cultivation-checkbox" class="checkbox-label">神魂功法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-dual-cultivation-checkbox" name="cultivation.dualCultivation.enabled" />
                                <label for="cultivation-dual-cultivation-checkbox" class="checkbox-label">双修功法</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-sword-cultivation-checkbox" name="cultivation.swordCultivation.enabled" />
                                <label for="cultivation-sword-cultivation-checkbox" class="checkbox-label">剑修功法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-alchemy-checkbox" name="cultivation.alchemy.enabled" />
                                <label for="cultivation-alchemy-checkbox" class="checkbox-label">炼丹术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-formation-checkbox" name="cultivation.formation.enabled" />
                                <label for="cultivation-formation-checkbox" class="checkbox-label">阵法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-talisman-checkbox" name="cultivation.talisman.enabled" />
                                <label for="cultivation-talisman-checkbox" class="checkbox-label">符箓术</label>
                            </div>
                        </div>
                    </div>

                    <!-- 灵力系统 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-spiritual-power-checkbox" name="cultivation.spiritualPower.enabled" checked />
                                <label for="cultivation-spiritual-power-checkbox" class="checkbox-label">灵力值</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-spiritual-root-checkbox" name="cultivation.spiritualRoot.enabled" />
                                <label for="cultivation-spiritual-root-checkbox" class="checkbox-label">灵根资质</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-meridians-checkbox" name="cultivation.meridians.enabled" />
                                <label for="cultivation-meridians-checkbox" class="checkbox-label">经脉系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-dantian-checkbox" name="cultivation.dantian.enabled" />
                                <label for="cultivation-dantian-checkbox" class="checkbox-label">丹田气海</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-divine-sense-checkbox" name="cultivation.divineSense.enabled" />
                                <label for="cultivation-divine-sense-checkbox" class="checkbox-label">神识</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-life-span-checkbox" name="cultivation.lifeSpan.enabled" />
                                <label for="cultivation-life-span-checkbox" class="checkbox-label">寿元</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-karma-checkbox" name="cultivation.karma.enabled" />
                                <label for="cultivation-karma-checkbox" class="checkbox-label">因果业力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-heavenly-dao-checkbox" name="cultivation.heavenlyDao.enabled" />
                                <label for="cultivation-heavenly-dao-checkbox" class="checkbox-label">天道感悟</label>
                            </div>
                        </div>
                    </div>

                    <!-- 法宝装备 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-flying-sword-checkbox" name="cultivation.flyingSword.enabled" />
                                <label for="cultivation-flying-sword-checkbox" class="checkbox-label">飞剑</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-magic-treasure-checkbox" name="cultivation.magicTreasure.enabled" />
                                <label for="cultivation-magic-treasure-checkbox" class="checkbox-label">法宝</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-spiritual-armor-checkbox" name="cultivation.spiritualArmor.enabled" />
                                <label for="cultivation-spiritual-armor-checkbox" class="checkbox-label">灵甲</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-storage-ring-checkbox" name="cultivation.storageRing.enabled" />
                                <label for="cultivation-storage-ring-checkbox" class="checkbox-label">储物戒</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-spirit-beast-checkbox" name="cultivation.spiritBeast.enabled" />
                                <label for="cultivation-spirit-beast-checkbox" class="checkbox-label">灵兽</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-puppet-checkbox" name="cultivation.puppet.enabled" />
                                <label for="cultivation-puppet-checkbox" class="checkbox-label">傀儡</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-avatar-checkbox" name="cultivation.avatar.enabled" />
                                <label for="cultivation-avatar-checkbox" class="checkbox-label">化身</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-clone-checkbox" name="cultivation.clone.enabled" />
                                <label for="cultivation-clone-checkbox" class="checkbox-label">分身</label>
                            </div>
                        </div>
                    </div>

                    <!-- 修炼资源 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-spirit-stone-checkbox" name="cultivation.spiritStone.enabled" />
                                <label for="cultivation-spirit-stone-checkbox" class="checkbox-label">灵石</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-spirit-herb-checkbox" name="cultivation.spiritHerb.enabled" />
                                <label for="cultivation-spirit-herb-checkbox" class="checkbox-label">灵草</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-pill-checkbox" name="cultivation.pill.enabled" />
                                <label for="cultivation-pill-checkbox" class="checkbox-label">丹药</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-spirit-vein-checkbox" name="cultivation.spiritVein.enabled" />
                                <label for="cultivation-spirit-vein-checkbox" class="checkbox-label">灵脉</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-cave-mansion-checkbox" name="cultivation.caveMansion.enabled" />
                                <label for="cultivation-cave-mansion-checkbox" class="checkbox-label">洞府</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-secret-realm-checkbox" name="cultivation.secretRealm.enabled" />
                                <label for="cultivation-secret-realm-checkbox" class="checkbox-label">秘境</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-inheritance-checkbox" name="cultivation.inheritance.enabled" />
                                <label for="cultivation-inheritance-checkbox" class="checkbox-label">传承</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-opportunity-checkbox" name="cultivation.opportunity.enabled" />
                                <label for="cultivation-opportunity-checkbox" class="checkbox-label">机缘</label>
                            </div>
                        </div>
                    </div>

                    <!-- 修炼活动 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-meditation-checkbox" name="cultivation.meditation.enabled" />
                                <label for="cultivation-meditation-checkbox" class="checkbox-label">打坐修炼</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-tribulation-crossing-checkbox" name="cultivation.tribulationCrossing.enabled" />
                                <label for="cultivation-tribulation-crossing-checkbox" class="checkbox-label">渡劫</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-enlightenment-checkbox" name="cultivation.enlightenment.enabled" />
                                <label for="cultivation-enlightenment-checkbox" class="checkbox-label">顿悟</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-breakthrough-checkbox" name="cultivation.breakthrough.enabled" />
                                <label for="cultivation-breakthrough-checkbox" class="checkbox-label">突破</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-sect-checkbox" name="cultivation.sect.enabled" />
                                <label for="cultivation-sect-checkbox" class="checkbox-label">宗门</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-master-disciple-checkbox" name="cultivation.masterDisciple.enabled" />
                                <label for="cultivation-master-disciple-checkbox" class="checkbox-label">师徒关系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-dao-companion-checkbox" name="cultivation.daoCompanion.enabled" />
                                <label for="cultivation-dao-companion-checkbox" class="checkbox-label">道侣</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="cultivation-immortal-ascension-checkbox" name="cultivation.immortalAscension.enabled" />
                                <label for="cultivation-immortal-ascension-checkbox" class="checkbox-label">飞升</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建玄幻世界面板
     */
    createFantasyPanel() {
        return `
            <div class="content-header">
                <h3>玄幻世界配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="fantasy-toggle" name="fantasy.enabled" checked />
                    <label for="fantasy-toggle" class="switch-slider"></label>
                </div>
            </div>

            <div class="content-body">
                <!-- 玄幻世界卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <div class="card-icon">🐉</div>
                        <div class="card-title">玄幻世界</div>
                        <div class="card-subtitle">奇幻魔法世界设定</div>
                    </div>
                    <div class="card-content">
                        <div class="card-status">
                            <span class="status-badge enabled">已启用</span>
                            <span class="status-count">19/52 项已配置</span>
                        </div>
                    </div>
                </div>

                <!-- 子项配置 -->
                <div class="sub-items">
                    <!-- 种族系统 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-human-checkbox" name="fantasy.human.enabled" checked />
                                <label for="fantasy-human-checkbox" class="checkbox-label">人类</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-elf-checkbox" name="fantasy.elf.enabled" checked />
                                <label for="fantasy-elf-checkbox" class="checkbox-label">精灵</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-dwarf-checkbox" name="fantasy.dwarf.enabled" checked />
                                <label for="fantasy-dwarf-checkbox" class="checkbox-label">矮人</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-orc-checkbox" name="fantasy.orc.enabled" />
                                <label for="fantasy-orc-checkbox" class="checkbox-label">兽人</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-dragon-checkbox" name="fantasy.dragon.enabled" />
                                <label for="fantasy-dragon-checkbox" class="checkbox-label">龙族</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-demon-checkbox" name="fantasy.demon.enabled" />
                                <label for="fantasy-demon-checkbox" class="checkbox-label">魔族</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-angel-checkbox" name="fantasy.angel.enabled" />
                                <label for="fantasy-angel-checkbox" class="checkbox-label">天使</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-undead-checkbox" name="fantasy.undead.enabled" />
                                <label for="fantasy-undead-checkbox" class="checkbox-label">不死族</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-halfling-checkbox" name="fantasy.halfling.enabled" />
                                <label for="fantasy-halfling-checkbox" class="checkbox-label">半身人</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-giant-checkbox" name="fantasy.giant.enabled" />
                                <label for="fantasy-giant-checkbox" class="checkbox-label">巨人</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-fairy-checkbox" name="fantasy.fairy.enabled" />
                                <label for="fantasy-fairy-checkbox" class="checkbox-label">妖精</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-vampire-checkbox" name="fantasy.vampire.enabled" />
                                <label for="fantasy-vampire-checkbox" class="checkbox-label">吸血鬼</label>
                            </div>
                        </div>
                    </div>

                    <!-- 魔法系统 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-fire-magic-checkbox" name="fantasy.fireMagic.enabled" checked />
                                <label for="fantasy-fire-magic-checkbox" class="checkbox-label">火系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-water-magic-checkbox" name="fantasy.waterMagic.enabled" />
                                <label for="fantasy-water-magic-checkbox" class="checkbox-label">水系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-earth-magic-checkbox" name="fantasy.earthMagic.enabled" />
                                <label for="fantasy-earth-magic-checkbox" class="checkbox-label">土系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-air-magic-checkbox" name="fantasy.airMagic.enabled" />
                                <label for="fantasy-air-magic-checkbox" class="checkbox-label">风系魔法</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-light-magic-checkbox" name="fantasy.lightMagic.enabled" />
                                <label for="fantasy-light-magic-checkbox" class="checkbox-label">光系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-dark-magic-checkbox" name="fantasy.darkMagic.enabled" />
                                <label for="fantasy-dark-magic-checkbox" class="checkbox-label">暗系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-nature-magic-checkbox" name="fantasy.natureMagic.enabled" />
                                <label for="fantasy-nature-magic-checkbox" class="checkbox-label">自然魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-space-magic-checkbox" name="fantasy.spaceMagic.enabled" />
                                <label for="fantasy-space-magic-checkbox" class="checkbox-label">空间魔法</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-time-magic-checkbox" name="fantasy.timeMagic.enabled" />
                                <label for="fantasy-time-magic-checkbox" class="checkbox-label">时间魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-necromancy-checkbox" name="fantasy.necromancy.enabled" />
                                <label for="fantasy-necromancy-checkbox" class="checkbox-label">死灵魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-illusion-magic-checkbox" name="fantasy.illusionMagic.enabled" />
                                <label for="fantasy-illusion-magic-checkbox" class="checkbox-label">幻术魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-enchantment-checkbox" name="fantasy.enchantment.enabled" />
                                <label for="fantasy-enchantment-checkbox" class="checkbox-label">附魔魔法</label>
                            </div>
                        </div>
                    </div>

                    <!-- 职业系统 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-warrior-checkbox" name="fantasy.warrior.enabled" />
                                <label for="fantasy-warrior-checkbox" class="checkbox-label">战士</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-mage-checkbox" name="fantasy.mage.enabled" />
                                <label for="fantasy-mage-checkbox" class="checkbox-label">法师</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-archer-checkbox" name="fantasy.archer.enabled" />
                                <label for="fantasy-archer-checkbox" class="checkbox-label">弓箭手</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-rogue-checkbox" name="fantasy.rogue.enabled" />
                                <label for="fantasy-rogue-checkbox" class="checkbox-label">盗贼</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-priest-checkbox" name="fantasy.priest.enabled" />
                                <label for="fantasy-priest-checkbox" class="checkbox-label">牧师</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-paladin-checkbox" name="fantasy.paladin.enabled" />
                                <label for="fantasy-paladin-checkbox" class="checkbox-label">圣骑士</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-druid-checkbox" name="fantasy.druid.enabled" />
                                <label for="fantasy-druid-checkbox" class="checkbox-label">德鲁伊</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-warlock-checkbox" name="fantasy.warlock.enabled" />
                                <label for="fantasy-warlock-checkbox" class="checkbox-label">术士</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-bard-checkbox" name="fantasy.bard.enabled" />
                                <label for="fantasy-bard-checkbox" class="checkbox-label">吟游诗人</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-monk-checkbox" name="fantasy.monk.enabled" />
                                <label for="fantasy-monk-checkbox" class="checkbox-label">武僧</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-ranger-checkbox" name="fantasy.ranger.enabled" />
                                <label for="fantasy-ranger-checkbox" class="checkbox-label">游侠</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-assassin-checkbox" name="fantasy.assassin.enabled" />
                                <label for="fantasy-assassin-checkbox" class="checkbox-label">刺客</label>
                            </div>
                        </div>
                    </div>

                    <!-- 神话生物 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-phoenix-checkbox" name="fantasy.phoenix.enabled" />
                                <label for="fantasy-phoenix-checkbox" class="checkbox-label">凤凰</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-unicorn-checkbox" name="fantasy.unicorn.enabled" />
                                <label for="fantasy-unicorn-checkbox" class="checkbox-label">独角兽</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-griffin-checkbox" name="fantasy.griffin.enabled" />
                                <label for="fantasy-griffin-checkbox" class="checkbox-label">狮鹫</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-pegasus-checkbox" name="fantasy.pegasus.enabled" />
                                <label for="fantasy-pegasus-checkbox" class="checkbox-label">飞马</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-kraken-checkbox" name="fantasy.kraken.enabled" />
                                <label for="fantasy-kraken-checkbox" class="checkbox-label">海妖</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-chimera-checkbox" name="fantasy.chimera.enabled" />
                                <label for="fantasy-chimera-checkbox" class="checkbox-label">奇美拉</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-basilisk-checkbox" name="fantasy.basilisk.enabled" />
                                <label for="fantasy-basilisk-checkbox" class="checkbox-label">蛇怪</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-hydra-checkbox" name="fantasy.hydra.enabled" />
                                <label for="fantasy-hydra-checkbox" class="checkbox-label">九头蛇</label>
                            </div>
                        </div>
                    </div>

                    <!-- 神器装备 -->
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-legendary-weapon-checkbox" name="fantasy.legendaryWeapon.enabled" />
                                <label for="fantasy-legendary-weapon-checkbox" class="checkbox-label">传说武器</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-magic-armor-checkbox" name="fantasy.magicArmor.enabled" />
                                <label for="fantasy-magic-armor-checkbox" class="checkbox-label">魔法护甲</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-artifact-checkbox" name="fantasy.artifact.enabled" />
                                <label for="fantasy-artifact-checkbox" class="checkbox-label">神器</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-relic-checkbox" name="fantasy.relic.enabled" />
                                <label for="fantasy-relic-checkbox" class="checkbox-label">圣遗物</label>
                            </div>
                        </div>
                    </div>

                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-magic-crystal-checkbox" name="fantasy.magicCrystal.enabled" />
                                <label for="fantasy-magic-crystal-checkbox" class="checkbox-label">魔法水晶</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-enchanted-item-checkbox" name="fantasy.enchantedItem.enabled" />
                                <label for="fantasy-enchanted-item-checkbox" class="checkbox-label">附魔物品</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-potion-checkbox" name="fantasy.potion.enabled" />
                                <label for="fantasy-potion-checkbox" class="checkbox-label">魔法药剂</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="fantasy-scroll-checkbox" name="fantasy.scroll.enabled" />
                                <label for="fantasy-scroll-checkbox" class="checkbox-label">魔法卷轴</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建都市现代面板
     */
    createModernPanel() {
        return `
            <div class="content-header">
                <h3>都市现代配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="modern-toggle" name="modern.enabled" checked />
                    <label for="modern-toggle" class="switch-slider"></label>
                </div>
                <div class="status-display">
                    <span class="status-text">8/52 项已配置</span>
                </div>
            </div>

            <div class="content-body">
                <div class="config-section">
                    <h4>🏙️ 城市生活</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-city-checkbox" name="modern.city.enabled" checked />
                                <label for="modern-city-checkbox" class="checkbox-label">居住城市</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-district-checkbox" name="modern.district.enabled" checked />
                                <label for="modern-district-checkbox" class="checkbox-label">所在区域</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-housing-checkbox" name="modern.housing.enabled" />
                                <label for="modern-housing-checkbox" class="checkbox-label">住房类型</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-transport-checkbox" name="modern.transport.enabled" checked />
                                <label for="modern-transport-checkbox" class="checkbox-label">交通方式</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-neighborhood-checkbox" name="modern.neighborhood.enabled" />
                                <label for="modern-neighborhood-checkbox" class="checkbox-label">社区环境</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-facilities-checkbox" name="modern.facilities.enabled" />
                                <label for="modern-facilities-checkbox" class="checkbox-label">周边设施</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-cost-checkbox" name="modern.cost.enabled" />
                                <label for="modern-cost-checkbox" class="checkbox-label">生活成本</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-safety-checkbox" name="modern.safety.enabled" />
                                <label for="modern-safety-checkbox" class="checkbox-label">安全指数</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-pollution-checkbox" name="modern.pollution.enabled" />
                                <label for="modern-pollution-checkbox" class="checkbox-label">环境质量</label>
                            </div>
                        </div>
                    </div>

                    <h4>💼 职业发展</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-job-checkbox" name="modern.job.enabled" checked />
                                <label for="modern-job-checkbox" class="checkbox-label">当前职业</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-company-checkbox" name="modern.company.enabled" />
                                <label for="modern-company-checkbox" class="checkbox-label">工作单位</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-position-checkbox" name="modern.position.enabled" />
                                <label for="modern-position-checkbox" class="checkbox-label">职位级别</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-income-checkbox" name="modern.income.enabled" checked />
                                <label for="modern-income-checkbox" class="checkbox-label">收入水平</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-worktime-checkbox" name="modern.worktime.enabled" />
                                <label for="modern-worktime-checkbox" class="checkbox-label">工作时间</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-benefits-checkbox" name="modern.benefits.enabled" />
                                <label for="modern-benefits-checkbox" class="checkbox-label">福利待遇</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-career-checkbox" name="modern.career.enabled" />
                                <label for="modern-career-checkbox" class="checkbox-label">职业规划</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-skills-checkbox" name="modern.skills.enabled" />
                                <label for="modern-skills-checkbox" class="checkbox-label">专业技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-education-checkbox" name="modern.education.enabled" />
                                <label for="modern-education-checkbox" class="checkbox-label">教育背景</label>
                            </div>
                        </div>
                    </div>

                    <h4>📱 科技生活</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-smartphone-checkbox" name="modern.smartphone.enabled" checked />
                                <label for="modern-smartphone-checkbox" class="checkbox-label">智能手机</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-computer-checkbox" name="modern.computer.enabled" />
                                <label for="modern-computer-checkbox" class="checkbox-label">电脑设备</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-internet-checkbox" name="modern.internet.enabled" />
                                <label for="modern-internet-checkbox" class="checkbox-label">网络使用</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-social-checkbox" name="modern.social.enabled" checked />
                                <label for="modern-social-checkbox" class="checkbox-label">社交媒体</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-gaming-checkbox" name="modern.gaming.enabled" />
                                <label for="modern-gaming-checkbox" class="checkbox-label">游戏娱乐</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-streaming-checkbox" name="modern.streaming.enabled" />
                                <label for="modern-streaming-checkbox" class="checkbox-label">视频平台</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-shopping-checkbox" name="modern.shopping.enabled" />
                                <label for="modern-shopping-checkbox" class="checkbox-label">在线购物</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-payment-checkbox" name="modern.payment.enabled" />
                                <label for="modern-payment-checkbox" class="checkbox-label">移动支付</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-ai-checkbox" name="modern.ai.enabled" />
                                <label for="modern-ai-checkbox" class="checkbox-label">AI助手</label>
                            </div>
                        </div>
                    </div>

                    <h4>🏥 健康管理</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-health-checkbox" name="modern.health.enabled" />
                                <label for="modern-health-checkbox" class="checkbox-label">健康状况</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-fitness-checkbox" name="modern.fitness.enabled" />
                                <label for="modern-fitness-checkbox" class="checkbox-label">健身习惯</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-diet-checkbox" name="modern.diet.enabled" />
                                <label for="modern-diet-checkbox" class="checkbox-label">饮食习惯</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-sleep-checkbox" name="modern.sleep.enabled" />
                                <label for="modern-sleep-checkbox" class="checkbox-label">睡眠质量</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-medical-checkbox" name="modern.medical.enabled" />
                                <label for="modern-medical-checkbox" class="checkbox-label">医疗保险</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-stress-checkbox" name="modern.stress.enabled" />
                                <label for="modern-stress-checkbox" class="checkbox-label">压力管理</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-mental-checkbox" name="modern.mental.enabled" />
                                <label for="modern-mental-checkbox" class="checkbox-label">心理健康</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-checkup-checkbox" name="modern.checkup.enabled" />
                                <label for="modern-checkup-checkbox" class="checkbox-label">定期体检</label>
                            </div>
                        </div>
                    </div>

                    <h4>🛍️ 消费习惯</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-budget-checkbox" name="modern.budget.enabled" />
                                <label for="modern-budget-checkbox" class="checkbox-label">消费预算</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-brands-checkbox" name="modern.brands.enabled" />
                                <label for="modern-brands-checkbox" class="checkbox-label">品牌偏好</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-fashion-checkbox" name="modern.fashion.enabled" />
                                <label for="modern-fashion-checkbox" class="checkbox-label">时尚风格</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-luxury-checkbox" name="modern.luxury.enabled" />
                                <label for="modern-luxury-checkbox" class="checkbox-label">奢侈品消费</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-investment-checkbox" name="modern.investment.enabled" />
                                <label for="modern-investment-checkbox" class="checkbox-label">投资理财</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-saving-checkbox" name="modern.saving.enabled" />
                                <label for="modern-saving-checkbox" class="checkbox-label">储蓄习惯</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-credit-checkbox" name="modern.credit.enabled" />
                                <label for="modern-credit-checkbox" class="checkbox-label">信用记录</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-insurance-checkbox" name="modern.insurance.enabled" />
                                <label for="modern-insurance-checkbox" class="checkbox-label">保险配置</label>
                            </div>
                        </div>
                    </div>

                    <h4>🎭 娱乐休闲</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-movies-checkbox" name="modern.movies.enabled" />
                                <label for="modern-movies-checkbox" class="checkbox-label">电影偏好</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-music-checkbox" name="modern.music.enabled" />
                                <label for="modern-music-checkbox" class="checkbox-label">音乐品味</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-books-checkbox" name="modern.books.enabled" />
                                <label for="modern-books-checkbox" class="checkbox-label">阅读习惯</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-travel-checkbox" name="modern.travel.enabled" />
                                <label for="modern-travel-checkbox" class="checkbox-label">旅行经历</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-sports-checkbox" name="modern.sports.enabled" />
                                <label for="modern-sports-checkbox" class="checkbox-label">运动爱好</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-hobbies-checkbox" name="modern.hobbies.enabled" />
                                <label for="modern-hobbies-checkbox" class="checkbox-label">兴趣爱好</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-clubs-checkbox" name="modern.clubs.enabled" />
                                <label for="modern-clubs-checkbox" class="checkbox-label">社团活动</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="modern-events-checkbox" name="modern.events.enabled" />
                                <label for="modern-events-checkbox" class="checkbox-label">活动参与</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建历史古代面板
     */
    createHistoricalPanel() {
        return `
            <div class="content-header">
                <h3>历史古代配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="historical-toggle" name="historical.enabled" checked />
                    <label for="historical-toggle" class="switch-slider"></label>
                </div>
                <div class="status-display">
                    <span class="status-text">7/52 项已配置</span>
                </div>
            </div>

            <div class="content-body">
                <div class="config-section">
                    <h4>🏛️ 朝代背景</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-dynasty-checkbox" name="historical.dynasty.enabled" checked />
                                <label for="historical-dynasty-checkbox" class="checkbox-label">历史朝代</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-period-checkbox" name="historical.period.enabled" checked />
                                <label for="historical-period-checkbox" class="checkbox-label">历史时期</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-emperor-checkbox" name="historical.emperor.enabled" />
                                <label for="historical-emperor-checkbox" class="checkbox-label">在位皇帝</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-capital-checkbox" name="historical.capital.enabled" />
                                <label for="historical-capital-checkbox" class="checkbox-label">都城位置</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-region-checkbox" name="historical.region.enabled" />
                                <label for="historical-region-checkbox" class="checkbox-label">所在州府</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-events-checkbox" name="historical.events.enabled" />
                                <label for="historical-events-checkbox" class="checkbox-label">重大事件</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-wars-checkbox" name="historical.wars.enabled" />
                                <label for="historical-wars-checkbox" class="checkbox-label">战争背景</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-politics-checkbox" name="historical.politics.enabled" />
                                <label for="historical-politics-checkbox" class="checkbox-label">政治环境</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-economy-checkbox" name="historical.economy.enabled" />
                                <label for="historical-economy-checkbox" class="checkbox-label">经济状况</label>
                            </div>
                        </div>
                    </div>

                    <h4>👑 社会地位</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-class-checkbox" name="historical.class.enabled" checked />
                                <label for="historical-class-checkbox" class="checkbox-label">社会阶层</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-title-checkbox" name="historical.title.enabled" />
                                <label for="historical-title-checkbox" class="checkbox-label">官职爵位</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-family-checkbox" name="historical.family.enabled" checked />
                                <label for="historical-family-checkbox" class="checkbox-label">家族背景</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-wealth-checkbox" name="historical.wealth.enabled" />
                                <label for="historical-wealth-checkbox" class="checkbox-label">财富状况</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-land-checkbox" name="historical.land.enabled" />
                                <label for="historical-land-checkbox" class="checkbox-label">土地财产</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-servants-checkbox" name="historical.servants.enabled" />
                                <label for="historical-servants-checkbox" class="checkbox-label">仆从随从</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-influence-checkbox" name="historical.influence.enabled" />
                                <label for="historical-influence-checkbox" class="checkbox-label">政治影响</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-reputation-checkbox" name="historical.reputation.enabled" />
                                <label for="historical-reputation-checkbox" class="checkbox-label">社会声望</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-connections-checkbox" name="historical.connections.enabled" />
                                <label for="historical-connections-checkbox" class="checkbox-label">人脉关系</label>
                            </div>
                        </div>
                    </div>

                    <h4>📚 文化修养</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-education-checkbox" name="historical.education.enabled" checked />
                                <label for="historical-education-checkbox" class="checkbox-label">教育程度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-poetry-checkbox" name="historical.poetry.enabled" />
                                <label for="historical-poetry-checkbox" class="checkbox-label">诗词歌赋</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-calligraphy-checkbox" name="historical.calligraphy.enabled" />
                                <label for="historical-calligraphy-checkbox" class="checkbox-label">书法绘画</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-music-checkbox" name="historical.music.enabled" />
                                <label for="historical-music-checkbox" class="checkbox-label">音律乐器</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-chess-checkbox" name="historical.chess.enabled" />
                                <label for="historical-chess-checkbox" class="checkbox-label">棋艺博弈</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-classics-checkbox" name="historical.classics.enabled" />
                                <label for="historical-classics-checkbox" class="checkbox-label">经史子集</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-philosophy-checkbox" name="historical.philosophy.enabled" />
                                <label for="historical-philosophy-checkbox" class="checkbox-label">哲学思想</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-etiquette-checkbox" name="historical.etiquette.enabled" />
                                <label for="historical-etiquette-checkbox" class="checkbox-label">礼仪规范</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-language-checkbox" name="historical.language.enabled" />
                                <label for="historical-language-checkbox" class="checkbox-label">语言文字</label>
                            </div>
                        </div>
                    </div>

                    <h4>⚔️ 武艺技能</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-martial-checkbox" name="historical.martial.enabled" checked />
                                <label for="historical-martial-checkbox" class="checkbox-label">武艺水平</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-weapons-checkbox" name="historical.weapons.enabled" />
                                <label for="historical-weapons-checkbox" class="checkbox-label">兵器使用</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-archery-checkbox" name="historical.archery.enabled" />
                                <label for="historical-archery-checkbox" class="checkbox-label">弓箭射术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-horsemanship-checkbox" name="historical.horsemanship.enabled" />
                                <label for="historical-horsemanship-checkbox" class="checkbox-label">骑术马术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-strategy-checkbox" name="historical.strategy.enabled" />
                                <label for="historical-strategy-checkbox" class="checkbox-label">兵法战略</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-bodyguard-checkbox" name="historical.bodyguard.enabled" />
                                <label for="historical-bodyguard-checkbox" class="checkbox-label">护卫技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-hunting-checkbox" name="historical.hunting.enabled" />
                                <label for="historical-hunting-checkbox" class="checkbox-label">狩猎技巧</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-survival-checkbox" name="historical.survival.enabled" />
                                <label for="historical-survival-checkbox" class="checkbox-label">野外生存</label>
                            </div>
                        </div>
                    </div>

                    <h4>🏠 生活方式</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-residence-checkbox" name="historical.residence.enabled" />
                                <label for="historical-residence-checkbox" class="checkbox-label">居住环境</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-clothing-checkbox" name="historical.clothing.enabled" checked />
                                <label for="historical-clothing-checkbox" class="checkbox-label">服饰穿着</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-food-checkbox" name="historical.food.enabled" />
                                <label for="historical-food-checkbox" class="checkbox-label">饮食习惯</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-transport-checkbox" name="historical.transport.enabled" />
                                <label for="historical-transport-checkbox" class="checkbox-label">出行方式</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-entertainment-checkbox" name="historical.entertainment.enabled" />
                                <label for="historical-entertainment-checkbox" class="checkbox-label">娱乐活动</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-festivals-checkbox" name="historical.festivals.enabled" />
                                <label for="historical-festivals-checkbox" class="checkbox-label">节庆习俗</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-religion-checkbox" name="historical.religion.enabled" />
                                <label for="historical-religion-checkbox" class="checkbox-label">宗教信仰</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-medicine-checkbox" name="historical.medicine.enabled" />
                                <label for="historical-medicine-checkbox" class="checkbox-label">医药知识</label>
                            </div>
                        </div>
                    </div>

                    <h4>💼 职业技能</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-profession-checkbox" name="historical.profession.enabled" checked />
                                <label for="historical-profession-checkbox" class="checkbox-label">职业身份</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-crafts-checkbox" name="historical.crafts.enabled" />
                                <label for="historical-crafts-checkbox" class="checkbox-label">手工技艺</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-trade-checkbox" name="historical.trade.enabled" />
                                <label for="historical-trade-checkbox" class="checkbox-label">商贸经营</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-farming-checkbox" name="historical.farming.enabled" />
                                <label for="historical-farming-checkbox" class="checkbox-label">农业种植</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-administration-checkbox" name="historical.administration.enabled" />
                                <label for="historical-administration-checkbox" class="checkbox-label">行政管理</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-teaching-checkbox" name="historical.teaching.enabled" />
                                <label for="historical-teaching-checkbox" class="checkbox-label">教学传授</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-healing-checkbox" name="historical.healing.enabled" />
                                <label for="historical-healing-checkbox" class="checkbox-label">医术治疗</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="historical-construction-checkbox" name="historical.construction.enabled" />
                                <label for="historical-construction-checkbox" class="checkbox-label">建筑营造</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建魔法能力面板
     */
    createMagicPanel() {
        return `
            <div class="content-header">
                <h3>魔法能力配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="magic-toggle" name="magic.enabled" checked />
                    <label for="magic-toggle" class="switch-slider"></label>
                </div>
                <div class="status-display">
                    <span class="status-text">9/52 项已配置</span>
                </div>
            </div>

            <div class="content-body">
                <div class="config-section">
                    <h4>🔮 魔法学派</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-evocation-checkbox" name="magic.evocation.enabled" checked />
                                <label for="magic-evocation-checkbox" class="checkbox-label">塑能系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-illusion-checkbox" name="magic.illusion.enabled" checked />
                                <label for="magic-illusion-checkbox" class="checkbox-label">幻术系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-enchantment-checkbox" name="magic.enchantment.enabled" />
                                <label for="magic-enchantment-checkbox" class="checkbox-label">惑控系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-necromancy-checkbox" name="magic.necromancy.enabled" />
                                <label for="magic-necromancy-checkbox" class="checkbox-label">死灵系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-divination-checkbox" name="magic.divination.enabled" />
                                <label for="magic-divination-checkbox" class="checkbox-label">预言系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-transmutation-checkbox" name="magic.transmutation.enabled" />
                                <label for="magic-transmutation-checkbox" class="checkbox-label">变化系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-conjuration-checkbox" name="magic.conjuration.enabled" />
                                <label for="magic-conjuration-checkbox" class="checkbox-label">咒法系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-abjuration-checkbox" name="magic.abjuration.enabled" />
                                <label for="magic-abjuration-checkbox" class="checkbox-label">防护系</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-elemental-checkbox" name="magic.elemental.enabled" />
                                <label for="magic-elemental-checkbox" class="checkbox-label">元素系</label>
                            </div>
                        </div>
                    </div>

                    <h4>⚡ 法术等级</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-cantrip-checkbox" name="magic.cantrip.enabled" checked />
                                <label for="magic-cantrip-checkbox" class="checkbox-label">戏法(0环)</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-level1-checkbox" name="magic.level1.enabled" checked />
                                <label for="magic-level1-checkbox" class="checkbox-label">1环法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-level2-checkbox" name="magic.level2.enabled" />
                                <label for="magic-level2-checkbox" class="checkbox-label">2环法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-level3-checkbox" name="magic.level3.enabled" />
                                <label for="magic-level3-checkbox" class="checkbox-label">3环法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-level4-checkbox" name="magic.level4.enabled" />
                                <label for="magic-level4-checkbox" class="checkbox-label">4环法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-level5-checkbox" name="magic.level5.enabled" />
                                <label for="magic-level5-checkbox" class="checkbox-label">5环法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-level6-checkbox" name="magic.level6.enabled" />
                                <label for="magic-level6-checkbox" class="checkbox-label">6环法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-level7-checkbox" name="magic.level7.enabled" />
                                <label for="magic-level7-checkbox" class="checkbox-label">7环法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-level8-checkbox" name="magic.level8.enabled" />
                                <label for="magic-level8-checkbox" class="checkbox-label">8环法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-level9-checkbox" name="magic.level9.enabled" />
                                <label for="magic-level9-checkbox" class="checkbox-label">9环法术</label>
                            </div>
                        </div>
                    </div>

                    <h4>🧙 法师属性</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-level-checkbox" name="magic.level.enabled" checked />
                                <label for="magic-level-checkbox" class="checkbox-label">法师等级</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-mana-checkbox" name="magic.mana.enabled" checked />
                                <label for="magic-mana-checkbox" class="checkbox-label">法力值</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-intelligence-checkbox" name="magic.intelligence.enabled" />
                                <label for="magic-intelligence-checkbox" class="checkbox-label">智力属性</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-wisdom-checkbox" name="magic.wisdom.enabled" />
                                <label for="magic-wisdom-checkbox" class="checkbox-label">感知属性</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-charisma-checkbox" name="magic.charisma.enabled" />
                                <label for="magic-charisma-checkbox" class="checkbox-label">魅力属性</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-concentration-checkbox" name="magic.concentration.enabled" />
                                <label for="magic-concentration-checkbox" class="checkbox-label">专注能力</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-spellpower-checkbox" name="magic.spellpower.enabled" />
                                <label for="magic-spellpower-checkbox" class="checkbox-label">法术强度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-resistance-checkbox" name="magic.resistance.enabled" />
                                <label for="magic-resistance-checkbox" class="checkbox-label">魔法抗性</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-regeneration-checkbox" name="magic.regeneration.enabled" />
                                <label for="magic-regeneration-checkbox" class="checkbox-label">法力回复</label>
                            </div>
                        </div>
                    </div>

                    <h4>📚 法术书库</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-spellbook-checkbox" name="magic.spellbook.enabled" checked />
                                <label for="magic-spellbook-checkbox" class="checkbox-label">法术书</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-known-checkbox" name="magic.known.enabled" />
                                <label for="magic-known-checkbox" class="checkbox-label">已知法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-prepared-checkbox" name="magic.prepared.enabled" />
                                <label for="magic-prepared-checkbox" class="checkbox-label">准备法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-slots-checkbox" name="magic.slots.enabled" />
                                <label for="magic-slots-checkbox" class="checkbox-label">法术位</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-components-checkbox" name="magic.components.enabled" />
                                <label for="magic-components-checkbox" class="checkbox-label">法术材料</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-rituals-checkbox" name="magic.rituals.enabled" />
                                <label for="magic-rituals-checkbox" class="checkbox-label">仪式法术</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-metamagic-checkbox" name="magic.metamagic.enabled" />
                                <label for="magic-metamagic-checkbox" class="checkbox-label">超魔专长</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-scrolls-checkbox" name="magic.scrolls.enabled" />
                                <label for="magic-scrolls-checkbox" class="checkbox-label">法术卷轴</label>
                            </div>
                        </div>
                    </div>

                    <h4>🔥 元素魔法</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-fire-checkbox" name="magic.fire.enabled" checked />
                                <label for="magic-fire-checkbox" class="checkbox-label">火系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-water-checkbox" name="magic.water.enabled" />
                                <label for="magic-water-checkbox" class="checkbox-label">水系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-earth-checkbox" name="magic.earth.enabled" />
                                <label for="magic-earth-checkbox" class="checkbox-label">土系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-air-checkbox" name="magic.air.enabled" />
                                <label for="magic-air-checkbox" class="checkbox-label">风系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-lightning-checkbox" name="magic.lightning.enabled" />
                                <label for="magic-lightning-checkbox" class="checkbox-label">雷系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-ice-checkbox" name="magic.ice.enabled" />
                                <label for="magic-ice-checkbox" class="checkbox-label">冰系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-light-checkbox" name="magic.light.enabled" />
                                <label for="magic-light-checkbox" class="checkbox-label">光系魔法</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-dark-checkbox" name="magic.dark.enabled" />
                                <label for="magic-dark-checkbox" class="checkbox-label">暗系魔法</label>
                            </div>
                        </div>
                    </div>

                    <h4>🛡️ 魔法装备</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-staff-checkbox" name="magic.staff.enabled" />
                                <label for="magic-staff-checkbox" class="checkbox-label">法杖</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-wand-checkbox" name="magic.wand.enabled" />
                                <label for="magic-wand-checkbox" class="checkbox-label">魔杖</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-orb-checkbox" name="magic.orb.enabled" />
                                <label for="magic-orb-checkbox" class="checkbox-label">法球</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-robe-checkbox" name="magic.robe.enabled" />
                                <label for="magic-robe-checkbox" class="checkbox-label">法袍</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-amulet-checkbox" name="magic.amulet.enabled" />
                                <label for="magic-amulet-checkbox" class="checkbox-label">护身符</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-ring-checkbox" name="magic.ring.enabled" />
                                <label for="magic-ring-checkbox" class="checkbox-label">魔法戒指</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-crystal-checkbox" name="magic.crystal.enabled" />
                                <label for="magic-crystal-checkbox" class="checkbox-label">魔法水晶</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="magic-tome-checkbox" name="magic.tome.enabled" />
                                <label for="magic-tome-checkbox" class="checkbox-label">魔法典籍</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建调教系统面板
     */
    createTrainingPanel() {
        return `
            <div class="content-header">
                <h3>调教系统配置</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="training-toggle" name="training.enabled" checked />
                    <label for="training-toggle" class="switch-slider"></label>
                </div>
                <div class="status-display">
                    <span class="status-text">6/52 项已配置</span>
                </div>
            </div>

            <div class="content-body">
                <div class="config-section">
                    <h4>📚 基础训练</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-obedience-checkbox" name="training.obedience.enabled" checked />
                                <label for="training-obedience-checkbox" class="checkbox-label">服从训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-discipline-checkbox" name="training.discipline.enabled" checked />
                                <label for="training-discipline-checkbox" class="checkbox-label">纪律训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-etiquette-checkbox" name="training.etiquette.enabled" />
                                <label for="training-etiquette-checkbox" class="checkbox-label">礼仪训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-posture-checkbox" name="training.posture.enabled" />
                                <label for="training-posture-checkbox" class="checkbox-label">姿态训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-speech-checkbox" name="training.speech.enabled" />
                                <label for="training-speech-checkbox" class="checkbox-label">言语训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-behavior-checkbox" name="training.behavior.enabled" />
                                <label for="training-behavior-checkbox" class="checkbox-label">行为规范</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-attention-checkbox" name="training.attention.enabled" />
                                <label for="training-attention-checkbox" class="checkbox-label">注意力训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-patience-checkbox" name="training.patience.enabled" />
                                <label for="training-patience-checkbox" class="checkbox-label">耐心训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-focus-checkbox" name="training.focus.enabled" />
                                <label for="training-focus-checkbox" class="checkbox-label">专注训练</label>
                            </div>
                        </div>
                    </div>

                    <h4>🎯 技能训练</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-service-checkbox" name="training.service.enabled" checked />
                                <label for="training-service-checkbox" class="checkbox-label">服务技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-cooking-checkbox" name="training.cooking.enabled" />
                                <label for="training-cooking-checkbox" class="checkbox-label">烹饪技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-cleaning-checkbox" name="training.cleaning.enabled" />
                                <label for="training-cleaning-checkbox" class="checkbox-label">清洁技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-massage-checkbox" name="training.massage.enabled" />
                                <label for="training-massage-checkbox" class="checkbox-label">按摩技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-entertainment-checkbox" name="training.entertainment.enabled" />
                                <label for="training-entertainment-checkbox" class="checkbox-label">娱乐技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-music-checkbox" name="training.music.enabled" />
                                <label for="training-music-checkbox" class="checkbox-label">音乐技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-dance-checkbox" name="training.dance.enabled" />
                                <label for="training-dance-checkbox" class="checkbox-label">舞蹈技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-art-checkbox" name="training.art.enabled" />
                                <label for="training-art-checkbox" class="checkbox-label">艺术技能</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-language-checkbox" name="training.language.enabled" />
                                <label for="training-language-checkbox" class="checkbox-label">语言技能</label>
                            </div>
                        </div>
                    </div>

                    <h4>💪 体能训练</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-strength-checkbox" name="training.strength.enabled" />
                                <label for="training-strength-checkbox" class="checkbox-label">力量训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-endurance-checkbox" name="training.endurance.enabled" />
                                <label for="training-endurance-checkbox" class="checkbox-label">耐力训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-flexibility-checkbox" name="training.flexibility.enabled" />
                                <label for="training-flexibility-checkbox" class="checkbox-label">柔韧训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-balance-checkbox" name="training.balance.enabled" />
                                <label for="training-balance-checkbox" class="checkbox-label">平衡训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-coordination-checkbox" name="training.coordination.enabled" />
                                <label for="training-coordination-checkbox" class="checkbox-label">协调训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-agility-checkbox" name="training.agility.enabled" />
                                <label for="training-agility-checkbox" class="checkbox-label">敏捷训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-stamina-checkbox" name="training.stamina.enabled" />
                                <label for="training-stamina-checkbox" class="checkbox-label">体力训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-recovery-checkbox" name="training.recovery.enabled" />
                                <label for="training-recovery-checkbox" class="checkbox-label">恢复训练</label>
                            </div>
                        </div>
                    </div>

                    <h4>🧠 心理训练</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-confidence-checkbox" name="training.confidence.enabled" checked />
                                <label for="training-confidence-checkbox" class="checkbox-label">自信训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-stress-checkbox" name="training.stress.enabled" />
                                <label for="training-stress-checkbox" class="checkbox-label">抗压训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-emotion-checkbox" name="training.emotion.enabled" />
                                <label for="training-emotion-checkbox" class="checkbox-label">情绪控制</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-memory-checkbox" name="training.memory.enabled" />
                                <label for="training-memory-checkbox" class="checkbox-label">记忆训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-logic-checkbox" name="training.logic.enabled" />
                                <label for="training-logic-checkbox" class="checkbox-label">逻辑训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-creativity-checkbox" name="training.creativity.enabled" />
                                <label for="training-creativity-checkbox" class="checkbox-label">创造力训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-meditation-checkbox" name="training.meditation.enabled" />
                                <label for="training-meditation-checkbox" class="checkbox-label">冥想训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-mindfulness-checkbox" name="training.mindfulness.enabled" />
                                <label for="training-mindfulness-checkbox" class="checkbox-label">正念训练</label>
                            </div>
                        </div>
                    </div>

                    <h4>⚙️ 训练设置</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-intensity-checkbox" name="training.intensity.enabled" checked />
                                <label for="training-intensity-checkbox" class="checkbox-label">训练强度</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-duration-checkbox" name="training.duration.enabled" />
                                <label for="training-duration-checkbox" class="checkbox-label">训练时长</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-frequency-checkbox" name="training.frequency.enabled" />
                                <label for="training-frequency-checkbox" class="checkbox-label">训练频率</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-progress-checkbox" name="training.progress.enabled" />
                                <label for="training-progress-checkbox" class="checkbox-label">进度跟踪</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-rewards-checkbox" name="training.rewards.enabled" />
                                <label for="training-rewards-checkbox" class="checkbox-label">奖励系统</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-punishment-checkbox" name="training.punishment.enabled" />
                                <label for="training-punishment-checkbox" class="checkbox-label">惩罚机制</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-schedule-checkbox" name="training.schedule.enabled" />
                                <label for="training-schedule-checkbox" class="checkbox-label">训练计划</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-evaluation-checkbox" name="training.evaluation.enabled" />
                                <label for="training-evaluation-checkbox" class="checkbox-label">效果评估</label>
                            </div>
                        </div>
                    </div>

                    <h4>📊 高级功能</h4>
                    <div class="sub-item-row">
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-auto-checkbox" name="training.auto.enabled" checked />
                                <label for="training-auto-checkbox" class="checkbox-label">自动训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-adaptive-checkbox" name="training.adaptive.enabled" />
                                <label for="training-adaptive-checkbox" class="checkbox-label">自适应训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-ai-checkbox" name="training.ai.enabled" />
                                <label for="training-ai-checkbox" class="checkbox-label">AI辅助训练</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-analytics-checkbox" name="training.analytics.enabled" />
                                <label for="training-analytics-checkbox" class="checkbox-label">数据分析</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-reports-checkbox" name="training.reports.enabled" />
                                <label for="training-reports-checkbox" class="checkbox-label">训练报告</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-export-checkbox" name="training.export.enabled" />
                                <label for="training-export-checkbox" class="checkbox-label">数据导出</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-backup-checkbox" name="training.backup.enabled" />
                                <label for="training-backup-checkbox" class="checkbox-label">数据备份</label>
                            </div>
                        </div>
                        <div class="sub-item">
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="training-sync-checkbox" name="training.sync.enabled" />
                                <label for="training-sync-checkbox" class="checkbox-label">云端同步</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 错误处理
     */
    handleError(error) {
        this.errorCount++;
        console.error(`[InfoBarSettings] ❌ 错误 #${this.errorCount}:`, error);
    }

    /**
     * 创建主题预览网格
     */
    createThemePreviewGrid() {
        const themes = [
            {
                id: 'default-dark',
                name: '默认深色',
                description: '经典深色主题，护眼舒适',
                colors: { bg: '#1a1a1a', text: '#ffffff', primary: '#007bff', border: '#333' }
            },
            {
                id: 'default-light',
                name: '默认浅色',
                description: '清新浅色主题，简洁明亮',
                colors: { bg: '#ffffff', text: '#333333', primary: '#007bff', border: '#dee2e6' }
            },
            {
                id: 'ocean-blue',
                name: '海洋蓝',
                description: '深邃海洋风格，宁静专注',
                colors: { bg: '#0f1419', text: '#e6fffa', primary: '#00d4aa', border: '#1e3a8a' }
            },
            {
                id: 'forest-green',
                name: '森林绿',
                description: '自然森林风格，清新护眼',
                colors: { bg: '#0d1b0d', text: '#e8f5e8', primary: '#22c55e', border: '#166534' }
            },
            {
                id: 'sunset-orange',
                name: '夕阳橙',
                description: '温暖夕阳风格，活力四射',
                colors: { bg: '#1a0f0a', text: '#fff7ed', primary: '#f97316', border: '#c2410c' }
            },
            {
                id: 'purple-night',
                name: '紫夜',
                description: '神秘紫色风格，优雅高贵',
                colors: { bg: '#1a0f1a', text: '#f3e8ff', primary: '#a855f7', border: '#7c3aed' }
            },
            {
                id: 'cherry-blossom',
                name: '樱花粉',
                description: '浪漫樱花风格，温柔甜美',
                colors: { bg: '#fdf2f8', text: '#831843', primary: '#ec4899', border: '#f9a8d4' }
            },
            {
                id: 'golden-sand',
                name: '金沙',
                description: '奢华金色风格，典雅大气',
                colors: { bg: '#1a1611', text: '#fef3c7', primary: '#f59e0b', border: '#d97706' }
            },
            {
                id: 'ice-blue',
                name: '冰蓝',
                description: '清冷冰蓝风格，冷静理性',
                colors: { bg: '#0f1419', text: '#e0f2fe', primary: '#0ea5e9', border: '#0284c7' }
            },
            {
                id: 'rose-red',
                name: '玫瑰红',
                description: '热情玫瑰风格，浪漫激情',
                colors: { bg: '#1a0a0a', text: '#ffe4e6', primary: '#e11d48', border: '#be123c' }
            },
            {
                id: 'mint-green',
                name: '薄荷绿',
                description: '清新薄荷风格，舒缓放松',
                colors: { bg: '#f0fdf4', text: '#14532d', primary: '#10b981', border: '#a7f3d0' }
            },
            {
                id: 'lavender',
                name: '薰衣草',
                description: '淡雅薰衣草风格，宁静安详',
                colors: { bg: '#faf5ff', text: '#581c87', primary: '#8b5cf6', border: '#c4b5fd' }
            },
            {
                id: 'coffee-brown',
                name: '咖啡棕',
                description: '温暖咖啡风格，沉稳内敛',
                colors: { bg: '#1c1917', text: '#fef7ed', primary: '#a16207', border: '#78716c' }
            },
            {
                id: 'slate-gray',
                name: '石板灰',
                description: '现代石板风格，简约专业',
                colors: { bg: '#0f172a', text: '#f1f5f9', primary: '#64748b', border: '#475569' }
            },
            {
                id: 'custom',
                name: '自定义',
                description: '创建您的专属主题',
                colors: { bg: '#1a1a1a', text: '#ffffff', primary: '#007bff', border: '#333' }
            }
        ];

        return themes.map(theme => {
            const isActive = theme.id === 'default-dark' ? 'active' : '';
            const isCustom = theme.id === 'custom';
            const currentBadge = theme.id === 'default-dark' ? '<div class="current-badge">当前</div>' : '';

            return `
                <div class="theme-preview-card ${isActive}"
                     data-theme="${theme.id}"
                     data-custom="${isCustom}">
                    <div class="theme-preview-mini" style="background: ${theme.colors.bg}; border: 1px solid ${theme.colors.border};">
                        <div class="preview-header-mini" style="background: ${theme.colors.primary}; color: ${theme.colors.bg};">标题</div>
                        <div class="preview-content-mini" style="color: ${theme.colors.text};">内容</div>
                        <div class="preview-button-mini" style="background: ${theme.colors.primary}; color: ${theme.colors.bg};">按钮</div>
                    </div>
                    <div class="theme-info">
                        <h4>${theme.name}</h4>
                        <p>${theme.description}</p>
                    </div>
                    ${currentBadge}
                </div>
            `;
        }).join('');
    }

    /**
     * 获取状态信息
     */
    getStatus() {
        return {
            initialized: this.initialized,
            visible: this.visible,
            currentTab: this.currentTab,
            errorCount: this.errorCount
        };
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.modal) {
            this.modal.remove();
            this.modal = null;
        }
        
        this.initialized = false;
        console.log('[InfoBarSettings] 💥 设置界面已销毁');
    }
}
