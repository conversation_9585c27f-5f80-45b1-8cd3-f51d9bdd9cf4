---
type: "always_apply"
---

#重要规则！
- 每次任务开始动需要先检查tasks.md文件内的开发日志，了解当前进度。
- 你每一次任务都需要列出任务清单tasks，并在tasks.md内写入自己本次的任务清单，每完成一项需要在相应的任务清单内勾选已完成，不要删除过去的任务记录，在结尾记录新的任务清单。。
- 需要严格按照rule.md制定的规则去进行。
- 任务结束后，需要检查是否成功的完成了任务，是否有其他未修复的地方。

测试网站：http://127.0.0.1:8000/
不需要打开扩展管理页面，一切问题，都使用脚本在控制台内检查!
---
description: Information bar integration tool 开发规则 - 五步操作流程
globs: ["**/*.js", "**/*.mjs", "**/*.ts", "**/*.json", "**/*.md"]
alwaysApply: true
---

# 🚀 Information bar integration tool - 开发规则 (五步操作流程)

## 📋 项目概述

**项目名称**: Information bar integration tool
**项目类型**: SillyTavern 第三方扩展插件
**技术栈**: JavaScript ES6+, 模块化架构, 事件驱动
**架构模式**: 智能数据管理，智能提示词，智能系统

---

## 🔄 五步操作流程 (核心开发方法论)

### 📊 步骤1: 分析 (ANALYZE)
**目标**: 深入理解用户需求和项目现状

#### 1.1 需求分析
- **MUST**: 使用 `sequentialthinking` 工具进行深度思考
- **MUST**: 明确用户的具体需求和期望结果
- **MUST**: 识别问题的根本原因，而非表面症状
- **MUST**: 评估修改对现有系统的影响范围

#### 1.2 项目理解
- **MUST**: 检查相关模块的当前实现状态
- **MUST**: 理解模块间的依赖关系和数据流
- **MUST**: 确认是否涉及统一数据核心或智能系统
- **MUST**: 评估是否需要兼容性处理

#### 1.3 技术分析
```javascript
// 分析示例：检查模块依赖
const dependencies = {
    unifiedDataCore: window.InfoBarData,
    smartSystem: window.SillyTavernInfobar?.modules?.smartSystem,
    eventSystem: window.SillyTavernInfobar?.eventSource
};
```

### 🔍 步骤2: 研究 (RESEARCH)
**目标**: 深入研究技术方案和最佳实践

#### 2.1 技术方案研究
- **MUST**: 使用 `codebase-retrieval` 工具获取相关代码信息
- **MUST**: 研究现有的类似实现和模式
- **MUST**: 确定最适合的技术路径
- **MUST**: 考虑性能和兼容性影响

#### 2.2 架构适配研究
- **MUST**: 确认修改符合项目的模块化架构
- **MUST**: 研究是否需要新增模块或修改现有模块
- **MUST**: 确定数据流和事件流的处理方式
- **MUST**: 研究错误处理和降级方案

#### 2.3 最佳实践研究
- **MUST**: 遵循项目现有的代码风格和命名规范
- **MUST**: 研究类似功能的实现模式
- **MUST**: 确定日志和调试策略

### 📝 步骤3: 规划 (PLAN)
**目标**: 制定详细的实施计划

#### 3.1 实施计划
- **MUST**: 将复杂任务分解为小的、可管理的步骤
- **MUST**: 确定修改的优先级和顺序
- **MUST**: 规划测试和验证策略
- **MUST**: 考虑回滚方案

#### 3.2 模块设计
- **MUST**: 遵循单一职责原则
- **MUST**: 设计清晰的接口和依赖关系
- **MUST**: 规划错误处理和边界情况
- **MUST**: 考虑性能优化点

#### 3.3 数据流设计
```javascript
// 规划示例：数据流设计
const dataFlow = {
    input: "用户操作/API调用",
    processing: "模块处理逻辑",
    storage: "智能数据管理",
    output: "UI更新/事件触发"
};
```

### ⚡ 步骤4: 执行 (EXECUTE)
**目标**: 按计划实施代码修改

#### 4.1 代码实施原则
- **MUST**: 严格按照规划的步骤执行
- **MUST**: 每次只修改一个小的功能点
- **MUST**: 使用 `str-replace-editor` 进行精确修改
- **MUST**: 保持代码的向后兼容性

#### 4.2 模块开发规范
```javascript
// 模块开发模板
export class ModuleName {
    constructor(dependencies = {}) {
        // 🔧 依赖注入
        this.unifiedDataCore = dependencies.unifiedDataCore || window.InfoBarData;
        this.eventSystem = dependencies.eventSystem || window.SillyTavernInfobar?.eventSource;

        // 🚀 初始化
        this.initialized = false;
        this.errorCount = 0;

        console.log('[ModuleName] 模块初始化');
        this.init();
    }

    async init() {
        try {
            // 初始化逻辑
            this.initialized = true;
            console.log('[ModuleName] ✅ 模块初始化完成');
        } catch (error) {
            console.error('[ModuleName] ❌ 初始化失败:', error);
            this.handleError(error);
        }
    }

    handleError(error) {
        this.errorCount++;
        // 错误处理逻辑
    }
}
```

#### 4.3 错误处理标准
- **MUST**: 所有异步操作都要有 try-catch
- **MUST**: 提供有意义的错误日志
- **MUST**: 实现优雅降级
- **MUST**: 避免阻塞主流程

#### 4.4 性能优化要求
- **MUST**: 使用防抖和节流优化频繁操作
- **MUST**: 实现懒加载和按需初始化
- **MUST**: 避免内存泄漏
- **MUST**: 优化DOM操作

### ✅ 步骤5: 检测 (VALIDATE)
**目标**: 验证实施结果的正确性和完整性

#### 5.1 功能验证
- **MUST**: 使用 `playwright` 工具进行浏览器测试
- **MUST**: 验证核心功能是否正常工作
- **MUST**: 检查错误处理是否生效
- **MUST**: 确认性能没有明显下降

#### 5.2 兼容性检测
- **MUST**: 检查控制台是否有新的错误
- **MUST**: 验证与其他模块的兼容性
- **MUST**: 测试在不同场景下的表现
- **MUST**: 确认数据完整性

#### 5.3 代码质量检查
```javascript
// 检测示例：验证模块状态
const validateModule = (moduleName) => {
    const module = window.SillyTavernInfobar?.modules?.[moduleName];
    return {
        exists: !!module,
        initialized: module?.initialized || false,
        errorCount: module?.errorCount || 0,
        status: module?.getStatus?.() || 'unknown'
    };
};
```

#### 5.4 完整性确认
- **MUST**: 确认所有计划的功能都已实现
- **MUST**: 验证文档和注释的准确性
- **MUST**: 检查是否需要更新相关配置
- **MUST**: 确认用户需求得到满足
