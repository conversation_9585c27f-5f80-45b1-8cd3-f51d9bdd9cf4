/**
 * Information Bar Integration Tool - 主样式文件
 * 
 * 包含所有组件的样式定义：
 * - 基础样式和变量
 * - 模态框样式
 * - 表单组件样式
 * - 表格组件样式
 * - 按钮和交互样式
 * - 响应式设计
 */

/* ========== CSS变量定义 ========== */
:root {
    /* 主色调 */
    --primary-color: #007bff;
    --primary-hover: #0056b3;
    --primary-light: #e3f2fd;
    
    /* 状态颜色 */
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    
    /* 中性颜色 */
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #868e96;
    --text-light: #f8f9fa;
    
    /* 背景颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #343a40;
    --bg-light: #f1f3f4;
    
    /* 边框颜色 */
    --border-color: #dee2e6;
    --border-light: #e9ecef;
    --border-dark: #adb5bd;
    
    /* 阴影 */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* 圆角 */
    --border-radius: 0.375rem;
    --border-radius-sm: 0.25rem;
    --border-radius-lg: 0.5rem;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-size-sm: 0.875rem;
    --font-size: 1rem;
    --font-size-lg: 1.125rem;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 700;
    
    /* 过渡 */
    --transition: all 0.15s ease-in-out;
    --transition-fast: all 0.1s ease-in-out;
    --transition-slow: all 0.3s ease-in-out;
}

/* ========== 基础样式重置 ========== */
.info-bar-settings-modal *,
.data-table-modal * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* ========== 模态框基础样式 ========== */
.info-bar-settings-modal,
.data-table-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family);
    font-size: var(--font-size);
    color: var(--text-primary);
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    position: relative;
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 设置模态框特定尺寸 */
.info-bar-settings-modal .modal-content {
    width: 800px;
    height: 600px;
}

/* 数据表格模态框特定尺寸 */
.data-table-modal .modal-content {
    width: 1200px;
    height: 800px;
}

/* ========== 模态框头部 ========== */
.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.modal-header h2 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.close-btn:hover {
    color: var(--text-primary);
    background: var(--bg-light);
}

/* ========== 模态框主体 ========== */
.modal-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* ========== 模态框底部 ========== */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing) var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

/* ========== 按钮样式 ========== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing);
    font-size: var(--font-size);
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    transition: var(--transition);
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* 按钮尺寸 */
.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.btn-large {
    padding: var(--spacing) var(--spacing-lg);
    font-size: var(--font-size-lg);
}

/* 按钮颜色变体 */
.btn-primary {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--border-light);
    border-color: var(--border-dark);
}

.btn-success {
    color: white;
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-danger {
    color: white;
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-warning {
    color: var(--text-primary);
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-info {
    color: white;
    background-color: var(--info-color);
    border-color: var(--info-color);
}

/* ========== 标签页样式 ========== */
.settings-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.tab-btn {
    padding: var(--spacing-sm) var(--spacing);
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: var(--font-size);
    font-weight: var(--font-weight-medium);
}

.tab-btn:hover {
    color: var(--text-primary);
    background: var(--bg-light);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--bg-primary);
}

/* ========== 设置内容区域 ========== */
.settings-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* ========== 设置组样式 ========== */
.settings-group {
    margin-bottom: var(--spacing-xl);
}

.settings-group h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
}

/* ========== 表单组样式 ========== */
.form-group {
    margin-bottom: var(--spacing);
}

.form-group label {
    display: block;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm);
    font-size: var(--font-size);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 复选框样式 */
.checkbox-label {
    display: flex !important;
    align-items: center;
    cursor: pointer;
    font-weight: var(--font-weight-normal) !important;
}

.checkbox-label input[type="checkbox"] {
    width: auto !important;
    margin-right: var(--spacing-sm);
    margin-bottom: 0;
}

/* 范围输入样式 */
.form-group input[type="range"] {
    width: calc(100% - 60px);
    display: inline-block;
}

.range-value {
    display: inline-block;
    width: 50px;
    text-align: center;
    font-weight: var(--font-weight-medium);
    color: var(--primary-color);
    margin-left: var(--spacing-sm);
}

/* ========== 颜色选择器组 ========== */
.color-picker-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing);
}

.color-picker-group input[type="color"] {
    height: 40px;
    padding: 2px;
    cursor: pointer;
}

/* ========== 主题预览 ========== */
.theme-preview {
    margin-top: var(--spacing-lg);
}

.preview-box {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing);
    background: var(--bg-primary);
}

.preview-header {
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

.preview-content {
    margin-bottom: var(--spacing);
    color: var(--text-secondary);
}

.preview-button {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

/* ========== 面板管理样式 ========== */
.panels-list {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.panel-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing);
    border-bottom: 1px solid var(--border-light);
}

.panel-item:last-child {
    border-bottom: none;
}

.panel-info {
    display: flex;
    align-items: center;
    gap: var(--spacing);
}

.panel-name {
    font-weight: var(--font-weight-medium);
}

.panel-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.panel-status.enabled {
    background: var(--success-color);
    color: white;
}

.panel-status.disabled {
    background: var(--text-muted);
    color: white;
}

.panel-actions {
    display: flex;
    gap: var(--spacing-xs);
}

/* ========== API状态样式 ========== */
.api-status {
    background: var(--bg-light);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing);
    margin-top: var(--spacing-lg);
}

.status-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-value {
    font-weight: var(--font-weight-medium);
}

.status-value.status-success {
    color: var(--success-color);
}

.status-value.status-error {
    color: var(--danger-color);
}

.status-value.status-testing {
    color: var(--warning-color);
}

/* ========== 危险区域样式 ========== */
.danger-zone {
    border: 1px solid var(--danger-color);
    border-radius: var(--border-radius);
    padding: var(--spacing);
    background: rgba(220, 53, 69, 0.05);
}

.danger-zone h3 {
    color: var(--danger-color);
    border-bottom-color: var(--danger-color);
}

/* ========== 消息样式 ========== */
.message {
    padding: var(--spacing-sm) var(--spacing);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing);
    font-weight: var(--font-weight-medium);
}

.message-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.message-error {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.message-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.message-info {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(23, 162, 184, 0.2);
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
    .info-bar-settings-modal .modal-content,
    .data-table-modal .modal-content {
        width: 95vw;
        height: 95vh;
        max-width: none;
        max-height: none;
    }
    
    .modal-header {
        padding: var(--spacing-sm) var(--spacing);
    }
    
    .modal-footer {
        padding: var(--spacing-sm) var(--spacing);
        flex-wrap: wrap;
    }
    
    .settings-tabs {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: 1;
        min-width: 0;
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .color-picker-group {
        grid-template-columns: 1fr;
    }
    
    .panel-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .panel-actions {
        align-self: stretch;
        justify-content: flex-end;
    }
}

/* ========== 数据表格专用样式 ========== */

/* 工具栏样式 */
.table-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    flex-wrap: wrap;
    gap: var(--spacing);
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing);
    flex-wrap: wrap;
}

.search-box {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.search-input {
    width: 200px;
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.filter-select,
.filter-date {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    background: var(--bg-primary);
}

.view-options,
.batch-actions {
    display: flex;
    gap: var(--spacing-xs);
}

/* 表格容器样式 */
.table-container {
    flex: 1;
    overflow: auto;
    position: relative;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
}

.table-header {
    background: var(--bg-secondary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table-header th {
    padding: var(--spacing-sm) var(--spacing);
    text-align: left;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
}

.table-header th.sortable {
    cursor: pointer;
    user-select: none;
    transition: var(--transition);
}

.table-header th.sortable:hover {
    background: var(--bg-light);
}

.sort-icon {
    margin-left: var(--spacing-xs);
    color: var(--text-secondary);
}

.select-column {
    width: 40px;
    text-align: center;
}

.table-row {
    transition: var(--transition);
}

.table-row:hover {
    background: var(--bg-light);
}

.table-row:nth-child(even) {
    background: rgba(0, 0, 0, 0.02);
}

.table-row:nth-child(even):hover {
    background: var(--bg-light);
}

.table-cell {
    padding: var(--spacing-sm) var(--spacing);
    border-bottom: 1px solid var(--border-light);
    vertical-align: top;
    max-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table-cell[data-column="content"] {
    white-space: normal;
    word-break: break-word;
}

/* 状态徽章样式 */
.status-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
}

.status-active {
    background: var(--success-color);
    color: white;
}

.status-inactive {
    background: var(--text-muted);
    color: white;
}

.status-archived {
    background: var(--warning-color);
    color: var(--text-primary);
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

.action-buttons .btn {
    padding: 2px 6px;
    font-size: 0.75rem;
}

/* 加载状态样式 */
.table-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    z-index: 20;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.table-empty {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing);
    opacity: 0.5;
}

.table-empty h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.table-empty p {
    margin-bottom: var(--spacing-lg);
}

/* 分页样式 */
.table-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
    flex-wrap: wrap;
    gap: var(--spacing);
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: var(--spacing);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.page-size-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    background: var(--bg-primary);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.page-numbers {
    display: flex;
    gap: var(--spacing-xs);
}

.page-btn {
    min-width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* ========== 扩展按钮样式 ========== */
.info-bar-extension-buttons {
    display: flex;
    gap: var(--spacing-sm);
    margin: var(--spacing-sm) 0;
}

.extension-btn {
    padding: var(--spacing-sm) var(--spacing);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: var(--transition);
}

.extension-btn:hover {
    background: var(--primary-hover);
}

.extension-btn:active {
    transform: translateY(1px);
}

/* ========== 深色主题支持 ========== */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f8f9fa;
        --text-secondary: #adb5bd;
        --text-muted: #6c757d;
        --text-light: #212529;

        --bg-primary: #212529;
        --bg-secondary: #343a40;
        --bg-dark: #f8f9fa;
        --bg-light: #495057;

        --border-color: #495057;
        --border-light: #343a40;
        --border-dark: #6c757d;
    }

    .table-loading {
        background: rgba(33, 37, 41, 0.9);
    }

    .table-row:nth-child(even) {
        background: rgba(255, 255, 255, 0.02);
    }
}

/* API开关样式 */
.switch-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: 500;
}

.switch-label input[type="checkbox"] {
    position: relative;
    width: 50px;
    height: 24px;
    appearance: none;
    background: #ccc;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.switch-label input[type="checkbox"]:checked {
    background: var(--primary-color);
}

.switch-label input[type="checkbox"]::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.switch-label input[type="checkbox"]:checked::before {
    transform: translateX(26px);
}

/* 主题预览网格样式 */
.theme-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.theme-preview-card {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: var(--background-color);
}

.theme-preview-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.theme-preview-card.active {
    border-color: var(--primary-color);
    background: rgba(var(--primary-color-rgb), 0.1);
}

.theme-preview-mini {
    width: 100%;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
}

.preview-header-mini {
    height: 20px;
    display: flex;
    align-items: center;
    padding: 0 8px;
    font-size: 10px;
    font-weight: bold;
}

.preview-content-mini {
    flex: 1;
    padding: 4px 8px;
    font-size: 9px;
    display: flex;
    align-items: center;
}

.preview-button-mini {
    height: 16px;
    margin: 4px 8px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    font-weight: bold;
}

.theme-info h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: var(--text-color);
}

.theme-info p {
    margin: 0;
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.3;
}

.current-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--primary-color);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: bold;
}

.theme-description {
    color: var(--text-secondary);
    margin-bottom: 10px;
    font-style: italic;
}

.current-theme-info {
    background: rgba(var(--primary-color-rgb), 0.1);
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid var(--primary-color);
}

.api-config-content {
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
    margin-top: 15px;
}

/* ========== 新的信息栏设置界面样式 ========== */
.infobar-modal-new {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(8px);
}

.infobar-modal-new .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    cursor: pointer;
}

.infobar-modal-new .modal-container {
    background: #1a1a1a;
    border-radius: 8px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    width: 95%;
    max-width: 1400px;
    height: 90%;
    max-height: 900px;
    position: relative;
    display: flex;
    flex-direction: column;
    border: 1px solid #333;
    color: #ffffff;
}

/* 顶部标题栏 */
.infobar-modal-new .modal-header {
    background: linear-gradient(135deg, #4a5568, #2d3748);
    padding: 15px 25px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px 8px 0 0;
}

.infobar-modal-new .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.infobar-modal-new .header-icon {
    font-size: 20px;
}

.infobar-modal-new .modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.infobar-modal-new .header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.infobar-modal-new .success-notification {
    background: #48bb78;
    color: white;
    padding: 8px 15px;
    border-radius: 6px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.infobar-modal-new .success-icon {
    font-size: 16px;
}

.infobar-modal-new .modal-close {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    font-size: 24px;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.infobar-modal-new .modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 主体内容区域 */
.infobar-modal-new .modal-body {
    flex: 1;
    display: flex;
    flex-direction: row;
    overflow: hidden;
}

/* 左侧导航栏 */
.infobar-modal-new .sidebar-nav {
    width: 240px;
    background: #2d3748;
    border-right: 1px solid #333;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.infobar-modal-new .nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    color: #cbd5e0;
}

.infobar-modal-new .nav-item:hover {
    background: #4a5568;
    color: #ffffff;
}

.infobar-modal-new .nav-item.active {
    background: #4299e1;
    color: #ffffff;
    border-left-color: #63b3ed;
}

.infobar-modal-new .nav-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.infobar-modal-new .nav-text {
    font-size: 14px;
    font-weight: 500;
}

.infobar-modal-new .nav-bottom {
    margin-top: auto;
    padding: 20px;
}

.infobar-modal-new .btn-reset {
    width: 100%;
    background: #e53e3e;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.infobar-modal-new .btn-reset:hover {
    background: #c53030;
}

/* 右侧内容区域 */
.infobar-modal-new .content-area {
    flex: 1;
    background: #1a1a1a;
    overflow-y: auto;
}

.infobar-modal-new .content-panel {
    display: none;
    padding: 25px;
    height: 100%;
}

.infobar-modal-new .content-panel.active {
    display: block;
    animation: fadeInContent 0.3s ease;
}

@keyframes fadeInContent {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

/* 内容头部 */
.infobar-modal-new .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #333;
}

.infobar-modal-new .content-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
}

/* 开关样式 */
.infobar-modal-new .toggle-switch {
    position: relative;
}

.infobar-modal-new .toggle-switch input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.infobar-modal-new .switch-slider {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
    background: #4a5568;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.infobar-modal-new .switch-slider:before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.infobar-modal-new .toggle-switch input:checked + .switch-slider {
    background: #4299e1;
}

.infobar-modal-new .toggle-switch input:checked + .switch-slider:before {
    transform: translateX(30px);
}

/* 信息卡片 */
.infobar-modal-new .info-card {
    background: #2d3748;
    border: 1px solid #4a5568;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.infobar-modal-new .card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.infobar-modal-new .card-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    background: #4299e1;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.infobar-modal-new .card-title {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.infobar-modal-new .card-subtitle {
    font-size: 14px;
    color: #a0aec0;
}

.infobar-modal-new .card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.infobar-modal-new .status-badge {
    background: #48bb78;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.infobar-modal-new .status-badge.enabled {
    background: #48bb78;
}

.infobar-modal-new .status-badge.disabled {
    background: #718096;
}

.infobar-modal-new .status-count {
    color: #a0aec0;
    font-size: 14px;
}

/* 子项配置 */
.infobar-modal-new .sub-items {
    margin-top: 20px;
}

.infobar-modal-new .sub-item-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
}

.infobar-modal-new .sub-item {
    background: #1a1a1a;
    border: 1px solid #4a5568;
    border-radius: 6px;
    padding: 15px;
}

.infobar-modal-new .checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.infobar-modal-new .checkbox-wrapper input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #4299e1;
}

.infobar-modal-new .checkbox-label {
    font-weight: 500;
    color: #ffffff;
    font-size: 14px;
}

.infobar-modal-new .sub-item-desc {
    color: #a0aec0;
    font-size: 12px;
    margin-bottom: 10px;
}

.infobar-modal-new .sub-item-value {
    display: flex;
    gap: 8px;
    align-items: center;
}

.infobar-modal-new .value-input,
.infobar-modal-new .value-select {
    flex: 1;
    background: #2d3748;
    border: 1px solid #4a5568;
    color: #ffffff;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 13px;
}

.infobar-modal-new .value-input:focus,
.infobar-modal-new .value-select:focus {
    outline: none;
    border-color: #4299e1;
}

.infobar-modal-new .btn-config {
    background: #4299e1;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.infobar-modal-new .btn-config:hover {
    background: #3182ce;
}

/* 操作按钮区域 */
.infobar-modal-new .action-buttons {
    margin-top: 25px;
    padding-top: 15px;
    border-top: 1px solid #333;
    display: flex;
    gap: 10px;
}

.infobar-modal-new .btn-action {
    background: #4a5568;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.infobar-modal-new .btn-action:hover {
    background: #2d3748;
}

.infobar-modal-new .btn-action.btn-all {
    background: #48bb78;
}

.infobar-modal-new .btn-action.btn-all:hover {
    background: #38a169;
}

.infobar-modal-new .btn-action.btn-none {
    background: #e53e3e;
}

.infobar-modal-new .btn-action.btn-none:hover {
    background: #c53030;
}

.infobar-modal-new .btn-action.btn-basic {
    background: #4299e1;
}

.infobar-modal-new .btn-action.btn-basic:hover {
    background: #3182ce;
}

/* 底部操作栏 */
.infobar-modal-new .modal-footer {
    background: #2d3748;
    padding: 15px 25px;
    border-top: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 0 0 8px 8px;
}

.infobar-modal-new .footer-left {
    color: #a0aec0;
    font-size: 14px;
}

.infobar-modal-new .status-text {
    color: #48bb78;
    font-weight: 500;
}

.infobar-modal-new .footer-right {
    display: flex;
    gap: 10px;
}

.infobar-modal-new .btn-cancel {
    background: #718096;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.infobar-modal-new .btn-cancel:hover {
    background: #4a5568;
}

.infobar-modal-new .btn-save {
    background: #4299e1;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.infobar-modal-new .btn-save:hover {
    background: #3182ce;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .infobar-modal-new .modal-container {
        width: 98%;
        height: 95%;
    }

    .infobar-modal-new .sidebar-nav {
        width: 200px;
    }

    .infobar-modal-new .sub-item-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .infobar-modal-new .modal-container {
        width: 100%;
        height: 100%;
        border-radius: 0;
    }

    .infobar-modal-new .modal-body {
        flex-direction: column;
    }

    .infobar-modal-new .sidebar-nav {
        width: 100%;
        height: auto;
        max-height: 200px;
        flex-direction: row;
        overflow-x: auto;
        overflow-y: hidden;
    }

    .infobar-modal-new .nav-item {
        min-width: 120px;
        flex-shrink: 0;
    }

    .infobar-modal-new .nav-bottom {
        display: none;
    }
}

/* ========== 新的数据表格界面样式 ========== */
.datatable-modal-new {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(8px);
}

.datatable-modal-new .modal-container {
    background: #000000;
    border-radius: 8px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
    width: 95%;
    max-width: 1600px;
    height: 90%;
    max-height: 900px;
    position: relative;
    display: flex;
    flex-direction: column;
    border: 1px solid #333;
    color: #ffffff;
}

/* 数据表格顶部标题栏 */
.datatable-modal-new .modal-header {
    background: #1a1a1a;
    border-bottom: 1px solid #333;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px 8px 0 0;
}

.datatable-modal-new .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.datatable-modal-new .header-icon {
    font-size: 20px;
}

.datatable-modal-new .modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.datatable-modal-new .header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.datatable-modal-new .success-notification {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(72, 187, 120, 0.1);
    border: 1px solid rgba(72, 187, 120, 0.3);
    padding: 8px 12px;
    border-radius: 6px;
    color: #48bb78;
    font-size: 14px;
}

.datatable-modal-new .success-icon {
    font-size: 16px;
}

.datatable-modal-new .modal-close {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    font-size: 24px;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.datatable-modal-new .modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 顶部工具栏 */
.datatable-modal-new .table-toolbar {
    background: #1a1a1a;
    border-bottom: 1px solid #333;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.datatable-modal-new .toolbar-left {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.datatable-modal-new .toolbar-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.datatable-modal-new .btn-tool {
    background: #2d3748;
    border: 1px solid #4a5568;
    color: #cbd5e0;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
}

.datatable-modal-new .btn-tool:hover {
    background: #4a5568;
    color: #ffffff;
    border-color: #63b3ed;
}

.datatable-modal-new .btn-tool.active {
    background: #4299e1;
    color: #ffffff;
    border-color: #63b3ed;
}

.datatable-modal-new .search-box {
    display: flex;
    align-items: center;
    background: #2d3748;
    border: 1px solid #4a5568;
    border-radius: 6px;
    overflow: hidden;
}

.datatable-modal-new .search-input {
    background: transparent;
    border: none;
    color: #ffffff;
    padding: 8px 12px;
    outline: none;
    width: 200px;
}

.datatable-modal-new .search-input::placeholder {
    color: #a0aec0;
}

.datatable-modal-new .btn-search {
    background: #4299e1;
    border: none;
    color: #ffffff;
    padding: 8px 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.datatable-modal-new .btn-search:hover {
    background: #3182ce;
}

/* 分组表格 */
.datatable-modal-new .grouped-tables {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #000000;
}

.datatable-modal-new .table-group {
    margin-bottom: 30px;
    border: 1px solid #333;
    border-radius: 8px;
    background: #111111;
}

.datatable-modal-new .group-header {
    background: #1a1a1a;
    padding: 15px 20px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.datatable-modal-new .group-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 16px;
}

.datatable-modal-new .group-icon {
    font-size: 18px;
}

.datatable-modal-new .group-count {
    color: #a0aec0;
    font-size: 14px;
    font-weight: normal;
}

.datatable-modal-new .group-actions {
    display: flex;
    gap: 8px;
}

.datatable-modal-new .btn-group-action {
    background: transparent;
    border: 1px solid #4a5568;
    color: #cbd5e0;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.datatable-modal-new .btn-group-action:hover {
    background: #4a5568;
    color: #ffffff;
}

.datatable-modal-new .group-content {
    display: none;
    padding: 0;
}

.datatable-modal-new .group-content.expanded {
    display: block;
}

/* 深色主题表格 */
.datatable-modal-new .data-table-container {
    overflow-x: auto;
}

.datatable-modal-new .dark-table {
    width: 100%;
    border-collapse: collapse;
    background: #000000;
    color: #ffffff;
}

.datatable-modal-new .dark-table th {
    background: #1a1a1a;
    color: #ffffff;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #333;
    border-right: 1px solid #333;
    font-size: 14px;
}

.datatable-modal-new .dark-table th:last-child {
    border-right: none;
}

.datatable-modal-new .dark-table td {
    padding: 10px 15px;
    border-bottom: 1px solid #333;
    border-right: 1px solid #333;
    font-size: 13px;
}

.datatable-modal-new .dark-table td:last-child {
    border-right: none;
}

.datatable-modal-new .data-row:hover {
    background: #1a1a1a;
}

.datatable-modal-new .data-row:nth-child(even) {
    background: #0a0a0a;
}

.datatable-modal-new .data-row:nth-child(even):hover {
    background: #1a1a1a;
}

/* 表格列宽 */
.datatable-modal-new .col-checkbox {
    width: 50px;
    text-align: center;
}

.datatable-modal-new .col-name {
    width: 150px;
}

.datatable-modal-new .col-value {
    width: 200px;
}

.datatable-modal-new .col-type {
    width: 100px;
}

.datatable-modal-new .col-status {
    width: 100px;
}

.datatable-modal-new .col-modified {
    width: 150px;
}

.datatable-modal-new .col-actions {
    width: 100px;
    text-align: center;
}

/* 状态徽章 */
.datatable-modal-new .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.datatable-modal-new .status-badge.active {
    background: #48bb78;
    color: #ffffff;
}

.datatable-modal-new .status-badge.inactive {
    background: #ed8936;
    color: #ffffff;
}

/* 操作按钮 */
.datatable-modal-new .btn-action {
    background: transparent;
    border: none;
    color: #cbd5e0;
    padding: 4px 6px;
    margin: 0 2px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.datatable-modal-new .btn-action:hover {
    background: #4a5568;
    color: #ffffff;
}

.datatable-modal-new .btn-action.edit:hover {
    background: #4299e1;
}

.datatable-modal-new .btn-action.delete:hover {
    background: #f56565;
}

/* 复选框样式 */
.datatable-modal-new input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #4299e1;
    cursor: pointer;
}

/* 底部状态栏 */
.datatable-modal-new .modal-footer {
    background: #1a1a1a;
    border-top: 1px solid #333;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #cbd5e0;
    font-size: 14px;
}

.datatable-modal-new .footer-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.datatable-modal-new .footer-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.datatable-modal-new .count-number {
    color: #4299e1;
    font-weight: 600;
}

.datatable-modal-new .btn-pagination {
    background: #2d3748;
    border: 1px solid #4a5568;
    color: #cbd5e0;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.datatable-modal-new .btn-pagination:hover {
    background: #4a5568;
    color: #ffffff;
}

.datatable-modal-new .btn-pagination:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.datatable-modal-new .page-info {
    margin: 0 10px;
}

.datatable-modal-new .current-page,
.datatable-modal-new .total-pages {
    color: #4299e1;
    font-weight: 600;
}

/* 数据表格响应式设计 */
@media (max-width: 768px) {
    .datatable-modal-new .modal-container {
        width: 98%;
        height: 95%;
    }

    .datatable-modal-new .table-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .datatable-modal-new .toolbar-left,
    .datatable-modal-new .toolbar-right {
        justify-content: center;
    }

    .datatable-modal-new .search-input {
        width: 150px;
    }

    .datatable-modal-new .dark-table {
        font-size: 12px;
    }

    .datatable-modal-new .dark-table th,
    .datatable-modal-new .dark-table td {
        padding: 8px 10px;
    }
}
